#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import sys
import sqlite3
import traceback
from datetime import datetime
import subprocess

# إعدادات التقرير
COL_WIDTHS_TABLE1 = [50, 50, 50, 40]  # جدول معلومات القسم
COL_WIDTHS_TABLE2 = [30, 60, 40, 30, 30, 30, 30, 30]  # جدول الأداءات الشهرية

TABLE1_HEADERS = ['القيمة', 'البيان', 'القيمة', 'البيان']
TABLE2_HEADERS = ['ملاحظات', 'اسم التلميذ', 'رمز التلميذ', 'المطلوب', 'المدفوع', 'المتبقي', 'حالة الدفع', 'تاريخ الدفع']

ROW_HEIGHT_TABLE1 = 8
ROW_HEIGHT_TABLE2 = 7
ROW_HEIGHT_HEADER = 10

PAGE_MARGIN_TOP = 0.2
PAGE_MARGIN_BOTTOM = 0.2
PAGE_MARGIN_LEFT = 10
PAGE_MARGIN_RIGHT = 10

try:
    from fpdf import FPDF
    import arabic_reshaper
    from bidi.algorithm import get_display
except ImportError:
    subprocess.check_call([sys.executable, "-m", "pip", "install", "fpdf2", "arabic-reshaper", "python-bidi"])
    from fpdf import FPDF
    import arabic_reshaper
    from bidi.algorithm import get_display

class ArabicPDF(FPDF):
    def __init__(self):
        super().__init__('P','mm','A4')
        self.set_margins(PAGE_MARGIN_LEFT, PAGE_MARGIN_TOP, PAGE_MARGIN_RIGHT)
        self.set_auto_page_break(auto=True, margin=PAGE_MARGIN_BOTTOM)
        fonts_dir = os.path.join(os.path.dirname(__file__), 'fonts')
        
        # إضافة خطوط Calibri إذا كانت متوفرة
        calibri_path = os.path.join(fonts_dir, 'calibri.ttf')
        calibri_bold_path = os.path.join(fonts_dir, 'calibrib.ttf')
        
        if os.path.exists(calibri_path):
            self.add_font('Calibri', '', calibri_path)
            self.calibri_available = True
        else:
            self.calibri_available = False
            
        if os.path.exists(calibri_bold_path):
            self.add_font('Calibri', 'B', calibri_bold_path)
            self.calibri_bold_available = True
        else:
            self.calibri_bold_available = False
        
        # تعيين الخط الافتراضي
        if self.calibri_available:
            self.set_font('Calibri', '', 13)
        else:
            self.set_font('Arial', '', 13)
        self.set_line_width(0.4)

    def set_title_font(self, size=15):
        """تعيين خط العناوين"""
        if self.calibri_bold_available:
            self.set_font('Calibri', 'B', size)
        else:
            self.set_font('Arial', 'B', size)

    def set_detail_font(self, size=13):
        """تعيين خط التفاصيل"""
        if self.calibri_bold_available:
            self.set_font('Calibri', 'B', size)
        else:
            self.set_font('Arial', 'B', size)

    def set_normal_font(self, size=12):
        """تعيين الخط العادي"""
        if self.calibri_available:
            self.set_font('Calibri', '', size)
        else:
            self.set_font('Arial', '', size)

    def ar_text(self, txt: str) -> str:
        """تحويل النص العربي ليتم عرضه بشكل صحيح"""
        reshaped = arabic_reshaper.reshape(str(txt))
        return get_display(reshaped)

def get_section_info_from_db(db_path, section):
    """جلب معلومات القسم من جدول_المواد_والاقسام"""
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # جلب معلومات القسم من جدول المواد والأقسام
        cursor.execute("""
            SELECT اسم_الاستاذ, المادة, المجموعة, نسبة_الواجبات
            FROM جدول_المواد_والاقسام
            WHERE القسم = ?
            ORDER BY المادة
        """, (section,))
        
        section_subjects = cursor.fetchall()
        
        # جلب إحصائيات التلاميذ في القسم
        cursor.execute("""
            SELECT COUNT(*) as total_students,
                   COUNT(CASE WHEN النوع = 'ذكر' THEN 1 END) as male_count,
                   COUNT(CASE WHEN النوع = 'أنثى' THEN 1 END) as female_count
            FROM جدول_البيانات
            WHERE القسم = ?
        """, (section,))
        
        student_stats = cursor.fetchone()
        
        conn.close()
        
        return {
            'section_subjects': section_subjects,
            'student_stats': student_stats
        }
        
    except Exception as e:
        print(f"خطأ في جلب معلومات القسم: {str(e)}")
        return None

def get_monthly_duties_by_section_month(db_path, section, month):
    """جلب الأداءات الشهرية للقسم في الشهر المحدد"""
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # ربط جدول monthly_duties مع جدول_البيانات للحصول على القسم
        cursor.execute("""
            SELECT 
                jb.اسم_التلميذ,
                jb.رمز_التلميذ,
                md.amount_required,
                md.amount_paid,
                md.amount_remaining,
                md.payment_status,
                md.payment_date,
                md.notes
            FROM monthly_duties md
            JOIN جدول_البيانات jb ON md.student_id = jb.id
            WHERE jb.القسم = ? AND md.month = ?
            ORDER BY jb.اسم_التلميذ
        """, (section, month))
        
        monthly_duties = cursor.fetchall()
        conn.close()
        
        return monthly_duties
        
    except Exception as e:
        print(f"خطأ في جلب الأداءات الشهرية: {str(e)}")
        return []

def generate_section_monthly_report(logo_path, section_info, monthly_duties, section, month, output_path):
    """إنشاء تقرير القسم الشهري"""
    pdf = ArabicPDF()
    margin = 10
    usable_w = pdf.w - 2 * margin

    pdf.add_page()
    y = pdf.get_y()
    
    # إضافة الشعار إذا كان متوفراً
    if logo_path and os.path.exists(logo_path):
        logo_w, logo_h = 60, 30
        x_logo = (pdf.w - logo_w) / 2
        pdf.image(logo_path, x=x_logo, y=y, w=logo_w, h=logo_h)
        y += logo_h + 5

    # عنوان التقرير
    pdf.set_draw_color(0, 51, 102)
    pdf.set_line_width(0.5)
    FIXED_BOX_HEIGHT = 12

    title_text = f"تقرير القسم الشهري - القسم: {section} - الشهر: {month}"
    
    pdf.set_text_color(0, 51, 102)
    pdf.set_xy(margin, y)
    pdf.set_title_font(15)
    pdf.cell(usable_w, FIXED_BOX_HEIGHT, pdf.ar_text(title_text), border=1, align='C')

    pdf.set_text_color(0, 0, 0)
    y += FIXED_BOX_HEIGHT + 5

    # الجدول الأول: معلومات القسم
    pdf.set_title_font(14)
    pdf.set_text_color(0, 100, 0)
    pdf.set_xy(margin, y)
    pdf.cell(usable_w, 8, pdf.ar_text('معلومات القسم والمواد'), border=0, align='C')
    pdf.set_text_color(0, 0, 0)
    y += 10

    cols1 = COL_WIDTHS_TABLE1
    
    # معلومات إحصائية عن القسم
    if section_info and section_info['student_stats']:
        stats = section_info['student_stats']
        section_info_rows = [
            [str(stats[0]), 'إجمالي التلاميذ', str(stats[1]), 'عدد الذكور'],
            [str(stats[2]), 'عدد الإناث', month, 'الشهر المحدد'],
        ]
        
        # إضافة معلومات المواد
        if section_info['section_subjects']:
            for i, subject in enumerate(section_info['section_subjects'][:3]):  # أول 3 مواد
                teacher_name = subject[0] or 'غير محدد'
                subject_name = subject[1] or 'غير محدد'
                group_name = subject[2] or 'غير محدد'
                duties_percent = str(subject[3]) + '%' if subject[3] else '100%'
                
                section_info_rows.append([
                    duties_percent, 'نسبة الواجبات', subject_name, f'المادة {i+1}'
                ])
                section_info_rows.append([
                    group_name, 'المجموعة', teacher_name, 'الأستاذ'
                ])
    else:
        section_info_rows = [
            ['0', 'إجمالي التلاميذ', '0', 'عدد الذكور'],
            ['0', 'عدد الإناث', month, 'الشهر المحدد'],
        ]
    
    pdf.set_detail_font(13)
    pdf.set_fill_color(230, 240, 255)
    
    # رسم صفوف معلومات القسم
    for row in section_info_rows:
        x = margin
        for i, cell in enumerate(row):
            pdf.set_xy(x, y)
            fill = i % 2 == 1
            align = 'C' if i % 2 == 1 else 'R'
            pdf.cell(cols1[i], ROW_HEIGHT_TABLE1, pdf.ar_text(cell), border=1, align=align, fill=fill)
            x += cols1[i]
        y += ROW_HEIGHT_TABLE1
        
    y += 5

    # الجدول الثاني: الأداءات الشهرية
    pdf.set_title_font(14)
    pdf.set_text_color(128, 0, 128)
    pdf.set_xy(margin, y)
    pdf.cell(usable_w, 8, pdf.ar_text(f'الأداءات الشهرية - {month}'), border=0, align='C')
    pdf.set_text_color(0, 0, 0)
    y += 10

    cols2 = COL_WIDTHS_TABLE2
    
    pdf.set_detail_font(13)
    pdf.set_fill_color(255, 200, 255)

    # رسم رأس الجدول الثاني
    x = margin
    for i, header in enumerate(TABLE2_HEADERS):
        pdf.set_xy(x, y)
        pdf.cell(cols2[i], ROW_HEIGHT_HEADER, pdf.ar_text(header), border=1, align='C', fill=True)
        x += cols2[i]

    y += ROW_HEIGHT_HEADER
    pdf.set_normal_font(11)

    # محتوى جدول الأداءات الشهرية
    if monthly_duties:
        for i, duty in enumerate(monthly_duties):
            x = margin
            data = [
                duty[7] or '',  # ملاحظات
                duty[0] or '',  # اسم التلميذ
                duty[1] or '',  # رمز التلميذ
                f'{float(duty[2]):.2f}' if duty[2] else '0.00',  # المطلوب
                f'{float(duty[3]):.2f}' if duty[3] else '0.00',  # المدفوع
                f'{float(duty[4]):.2f}' if duty[4] else '0.00',  # المتبقي
                duty[5] or 'غير محدد',  # حالة الدفع
                duty[6] or 'غير محدد'   # تاريخ الدفع
            ]

            # تلوين حسب حالة الدفع
            if duty[5] == "مدفوع كاملاً":
                pdf.set_fill_color(220, 255, 220)  # أخضر فاتح
            elif duty[5] == "مدفوع جزئياً":
                pdf.set_fill_color(255, 255, 200)  # أصفر فاتح
            else:
                pdf.set_fill_color(255, 220, 220)  # أحمر فاتح

            for j, cell in enumerate(data):
                pdf.set_xy(x, y)
                pdf.cell(cols2[j], ROW_HEIGHT_TABLE2, pdf.ar_text(cell), border=1, align='C', fill=True)
                x += cols2[j]

            y += ROW_HEIGHT_TABLE2

            # الانتقال إلى صفحة جديدة عند الحاجة
            if y > pdf.h - 50:
                pdf.add_page()
                y = pdf.get_y()
    else:
        # إذا لم توجد أداءات شهرية
        pdf.set_xy(margin, y)
        pdf.set_fill_color(255, 245, 245)
        pdf.cell(sum(cols2), ROW_HEIGHT_TABLE2, pdf.ar_text('لا توجد أداءات شهرية مسجلة لهذا القسم في هذا الشهر'), border=1, align='C', fill=True)
        y += ROW_HEIGHT_TABLE2

    # إضافة تاريخ الطباعة
    y += 10
    current_date = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    pdf.set_normal_font(10)
    pdf.set_xy(margin, y)
    pdf.cell(usable_w/2, 8, pdf.ar_text(f'تاريخ الطباعة: {current_date}'), border=0, align='R')

    os.makedirs(os.path.dirname(output_path), exist_ok=True)
    pdf.output(output_path)
    print(f"تم إنشاء تقرير القسم الشهري: {output_path}")

def print_section_monthly_report(parent=None, section=None, month=None):
    """
    دالة لإنشاء تقرير القسم الشهري
    
    المعاملات:
        parent: كائن النافذة الأم
        section: اسم القسم
        month: الشهر

    العوائد:
        (success, output_path, message): ثلاثية تحدد نجاح العملية ومسار الملف ورسالة النتيجة
    """
    try:
        if not section:
            return False, None, "اسم القسم غير محدد."
        
        if not month:
            return False, None, "الشهر غير محدد."

        # تحديد مسار قاعدة البيانات
        db_path = os.path.join(os.path.dirname(__file__), 'data.db')
        
        # جلب بيانات القسم والأداءات الشهرية
        try:
            # جلب شعار المؤسسة
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            cursor.execute("SELECT ImagePath1 FROM بيانات_المؤسسة LIMIT 1")
            logo_row = cursor.fetchone()
            logo_path = logo_row[0] if logo_row and os.path.exists(logo_row[0]) else None
            
            conn.close()
            
            # جلب معلومات القسم
            section_info = get_section_info_from_db(db_path, section)
            
            # جلب الأداءات الشهرية
            monthly_duties = get_monthly_duties_by_section_month(db_path, section, month)
            
        except Exception as db_error:
            print(f"خطأ في الوصول لقاعدة البيانات: {db_error}")
            return False, None, f"خطأ في الوصول لقاعدة البيانات: {str(db_error)}"

        # إنشاء مجلد التقارير إذا لم يكن موجوداً
        reports_dir = os.path.join(os.path.expanduser('~'), 'Desktop', 'تقارير الأقسام الشهرية')
        if not os.path.exists(reports_dir):
            os.makedirs(reports_dir)

        # تحديد اسم الملف
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        section_clean = section.replace(' ', '_').replace('/', '_')
        output_path = os.path.join(reports_dir, f"تقرير_القسم_{section_clean}_{month}_{timestamp}.pdf")

        # إنشاء التقرير
        generate_section_monthly_report(logo_path, section_info, monthly_duties, section, month, output_path)

        # فتح الملف بعد إنشائه
        try:
            if sys.platform == 'win32':
                os.startfile(output_path)
            elif sys.platform == 'darwin':  # macOS
                subprocess.call(['open', output_path])
            else:  # Linux
                subprocess.call(['xdg-open', output_path])
        except Exception as e:
            return True, output_path, f"تم إنشاء التقرير ولكن تعذر فتح الملف: {str(e)}"

        return True, output_path, "تم إنشاء تقرير القسم الشهري بنجاح."
        
    except Exception as e:
        traceback.print_exc()
        return False, None, f"حدث خطأ في إنشاء تقرير القسم الشهري: {str(e)}"

if __name__=='__main__':
    # مثال على الاستخدام
    try:
        success, output_path, message = print_section_monthly_report(
            section="قسم / 01", 
            month="يناير"
        )
        
        print(f"النتيجة: {success}")
        print(f"المسار: {output_path}")
        print(f"الرسالة: {message}")
        
    except Exception as e:
        print(f"خطأ في الاختبار: {e}")
        traceback.print_exc()
