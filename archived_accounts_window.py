#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *
from datetime import datetime
from archived_accounts_manager import ArchivedAccountsManager

class ArchivedAccountsWindow(QMainWindow):
    """نافذة إدارة الحسابات المرحلة"""
    
    def __init__(self):
        super().__init__()
        self.manager = ArchivedAccountsManager()
        self.init_ui()
        self.load_archived_months()
    
    def init_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("إدارة الحسابات المرحلة - نظام احترافي")
        self.setGeometry(100, 100, 1000, 700)
        self.setWindowIcon(QIcon("icon.png"))
        
        # الويدجت الرئيسي
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(central_widget)
        
        # العنوان
        title_label = QLabel("🏦 إدارة الحسابات المرحلة")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                font-size: 24px;
                font-weight: bold;
                color: #2c3e50;
                padding: 20px;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                border-radius: 10px;
                margin-bottom: 20px;
            }
        """)
        main_layout.addWidget(title_label)
        
        # قسم الترحيل الجديد
        archive_group = QGroupBox("📤 ترحيل شهر جديد")
        archive_group.setStyleSheet("""
            QGroupBox {
                font-size: 16px;
                font-weight: bold;
                color: #2c3e50;
                border: 2px solid #3498db;
                border-radius: 10px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 10px 0 10px;
            }
        """)
        
        archive_layout = QHBoxLayout(archive_group)
        
        # اختيار الشهر
        month_label = QLabel("الشهر:")
        month_label.setFont(QFont("Arial", 12, QFont.Bold))
        self.month_combo = QComboBox()
        self.month_combo.addItems([
            "يناير", "فبراير", "مارس", "أبريل", "مايو", "يونيو",
            "يوليو", "أغسطس", "سبتمبر", "أكتوبر", "نوفمبر", "ديسمبر"
        ])
        self.month_combo.setCurrentText(self.get_current_month())
        
        # اختيار السنة
        year_label = QLabel("السنة:")
        year_label.setFont(QFont("Arial", 12, QFont.Bold))
        self.year_spin = QSpinBox()
        self.year_spin.setRange(2020, 2030)
        self.year_spin.setValue(datetime.now().year)
        
        # خيار إعادة الترحيل
        self.force_update_check = QCheckBox("إعادة ترحيل (حذف الموجود)")
        self.force_update_check.setStyleSheet("color: #e74c3c; font-weight: bold;")
        
        # زر الترحيل
        archive_btn = QPushButton("🔄 ترحيل الشهر")
        archive_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                padding: 10px 20px;
                font-size: 14px;
                font-weight: bold;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #2ecc71;
            }
            QPushButton:pressed {
                background-color: #229954;
            }
        """)
        archive_btn.clicked.connect(self.archive_month)
        
        archive_layout.addWidget(month_label)
        archive_layout.addWidget(self.month_combo)
        archive_layout.addWidget(year_label)
        archive_layout.addWidget(self.year_spin)
        archive_layout.addWidget(self.force_update_check)
        archive_layout.addWidget(archive_btn)
        archive_layout.addStretch()
        
        main_layout.addWidget(archive_group)
        
        # جدول الشهور المرحلة
        table_group = QGroupBox("📋 الشهور المرحلة")
        table_group.setStyleSheet("""
            QGroupBox {
                font-size: 16px;
                font-weight: bold;
                color: #2c3e50;
                border: 2px solid #9b59b6;
                border-radius: 10px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 10px 0 10px;
            }
        """)
        
        table_layout = QVBoxLayout(table_group)
        
        # أزرار الإدارة
        buttons_layout = QHBoxLayout()
        
        refresh_btn = QPushButton("🔄 تحديث")
        refresh_btn.clicked.connect(self.load_archived_months)
        
        delete_btn = QPushButton("🗑️ حذف المحدد")
        delete_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                padding: 8px 16px;
                font-weight: bold;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        delete_btn.clicked.connect(self.delete_selected_month)
        
        buttons_layout.addWidget(refresh_btn)
        buttons_layout.addWidget(delete_btn)
        buttons_layout.addStretch()
        
        table_layout.addLayout(buttons_layout)
        
        # الجدول
        self.table = QTableWidget()
        self.table.setColumnCount(5)
        self.table.setHorizontalHeaderLabels([
            "الشهر", "السنة", "عدد السجلات", "تاريخ الترحيل", "آخر تحديث"
        ])
        
        # تنسيق الجدول
        self.table.setAlternatingRowColors(True)
        self.table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.table.horizontalHeader().setStretchLastSection(True)
        self.table.setStyleSheet("""
            QTableWidget {
                gridline-color: #bdc3c7;
                background-color: white;
                alternate-background-color: #ecf0f1;
            }
            QHeaderView::section {
                background-color: #34495e;
                color: white;
                padding: 8px;
                border: none;
                font-weight: bold;
            }
        """)
        
        table_layout.addWidget(self.table)
        main_layout.addWidget(table_group)
        
        # شريط الحالة
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        self.status_bar.showMessage("جاهز - نظام الحسابات المرحلة")
    
    def get_current_month(self):
        """جلب الشهر الحالي بالعربية"""
        months = [
            "يناير", "فبراير", "مارس", "أبريل", "مايو", "يونيو",
            "يوليو", "أغسطس", "سبتمبر", "أكتوبر", "نوفمبر", "ديسمبر"
        ]
        return months[datetime.now().month - 1]
    
    def archive_month(self):
        """ترحيل شهر جديد"""
        month = self.month_combo.currentText()
        year = self.year_spin.value()
        force_update = self.force_update_check.isChecked()
        
        # تأكيد العملية
        if force_update:
            reply = QMessageBox.question(
                self, "تأكيد إعادة الترحيل",
                f"هل أنت متأكد من إعادة ترحيل {month}/{year}؟\n"
                "سيتم حذف البيانات المرحلة السابقة!",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )
            if reply != QMessageBox.Yes:
                return
        
        # تنفيذ الترحيل
        self.status_bar.showMessage(f"جاري ترحيل {month}/{year}...")
        QApplication.processEvents()
        
        try:
            success, message = self.manager.archive_monthly_accounts(month, year, force_update)
            
            if success:
                QMessageBox.information(self, "نجح الترحيل", f"✅ {message}")
                self.load_archived_months()  # تحديث الجدول
                self.status_bar.showMessage(f"تم ترحيل {month}/{year} بنجاح")
            else:
                QMessageBox.warning(self, "فشل الترحيل", f"❌ {message}")
                self.status_bar.showMessage("فشل في الترحيل")
                
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في الترحيل:\n{str(e)}")
            self.status_bar.showMessage("خطأ في الترحيل")
    
    def load_archived_months(self):
        """تحميل الشهور المرحلة في الجدول"""
        try:
            archived_months = self.manager.get_archived_months()
            
            self.table.setRowCount(len(archived_months))
            
            for row, month_data in enumerate(archived_months):
                month, year, record_count, first_archive, last_archive = month_data
                
                # الشهر
                month_item = QTableWidgetItem(month)
                month_item.setTextAlignment(Qt.AlignCenter)
                month_item.setFont(QFont("Arial", 11, QFont.Bold))
                self.table.setItem(row, 0, month_item)
                
                # السنة
                year_item = QTableWidgetItem(str(year))
                year_item.setTextAlignment(Qt.AlignCenter)
                year_item.setFont(QFont("Arial", 11, QFont.Bold))
                self.table.setItem(row, 1, year_item)
                
                # عدد السجلات
                count_item = QTableWidgetItem(str(record_count))
                count_item.setTextAlignment(Qt.AlignCenter)
                count_item.setBackground(QColor("#e8f5e8"))
                self.table.setItem(row, 2, count_item)
                
                # تاريخ الترحيل
                first_date = first_archive.split()[0] if first_archive else "غير محدد"
                first_item = QTableWidgetItem(first_date)
                first_item.setTextAlignment(Qt.AlignCenter)
                self.table.setItem(row, 3, first_item)
                
                # آخر تحديث
                last_date = last_archive.split()[0] if last_archive else "غير محدد"
                last_item = QTableWidgetItem(last_date)
                last_item.setTextAlignment(Qt.AlignCenter)
                self.table.setItem(row, 4, last_item)
            
            self.status_bar.showMessage(f"تم تحميل {len(archived_months)} شهر مرحل")
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل البيانات:\n{str(e)}")
    
    def delete_selected_month(self):
        """حذف الشهر المحدد"""
        current_row = self.table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار شهر للحذف")
            return
        
        month = self.table.item(current_row, 0).text()
        year = int(self.table.item(current_row, 1).text())
        record_count = self.table.item(current_row, 2).text()
        
        # تأكيد الحذف
        reply = QMessageBox.question(
            self, "تأكيد الحذف",
            f"هل أنت متأكد من حذف ترحيل {month}/{year}؟\n"
            f"سيتم حذف {record_count} سجل نهائياً!",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            try:
                success, message = self.manager.delete_archived_month(month, year)
                
                if success:
                    QMessageBox.information(self, "تم الحذف", f"✅ {message}")
                    self.load_archived_months()  # تحديث الجدول
                    self.status_bar.showMessage(f"تم حذف {month}/{year}")
                else:
                    QMessageBox.warning(self, "فشل الحذف", f"❌ {message}")
                    
            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"حدث خطأ في الحذف:\n{str(e)}")

if __name__ == "__main__":
    app = QApplication(sys.argv)
    app.setStyle('Fusion')
    
    # تطبيق ثيم داكن اختياري
    palette = QPalette()
    palette.setColor(QPalette.Window, QColor(53, 53, 53))
    palette.setColor(QPalette.WindowText, Qt.white)
    app.setPalette(palette)
    
    window = ArchivedAccountsWindow()
    window.show()
    
    sys.exit(app.exec_())
