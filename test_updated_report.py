#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import sqlite3

def test_updated_section_report():
    """اختبار التقرير المحدث للقسم الشهري"""
    
    print("🔍 اختبار التقرير المحدث للقسم الشهري...")
    print("📋 التحديثات الجديدة:")
    print("   الجدول الأول:")
    print("     - العمود الرابع: إجمالي التلاميذ، عدد الذكور، عدد الإناث، المجموعة")
    print("     - العمود الثاني: باقي المعلومات")
    print("   الجدول الثاني:")
    print("     - تم إزالة عمود الملاحظات")
    print("     - تم إضافة عمود الرقم الترتيبي")
    print("     - تم عكس ترتيب الأعمدة")
    print()
    
    try:
        # التحقق من وجود قاعدة البيانات
        if not os.path.exists('data.db'):
            print("❌ ملف قاعدة البيانات غير موجود")
            return False
        
        # جلب أول قسم للاختبار
        conn = sqlite3.connect('data.db')
        cursor = conn.cursor()
        
        cursor.execute("SELECT DISTINCT القسم FROM جدول_البيانات WHERE القسم IS NOT NULL AND القسم != '' LIMIT 1")
        section_result = cursor.fetchone()
        
        if not section_result:
            print("❌ لا توجد أقسام في قاعدة البيانات")
            conn.close()
            return False
        
        test_section = section_result[0]
        test_month = "يناير"
        
        print(f"🎯 اختبار التقرير للقسم: {test_section}")
        print(f"📅 الشهر: {test_month}")
        
        conn.close()
        
        # اختبار استيراد وحدة print_section_monthly
        try:
            from print_section_monthly import print_section_monthly_report
            print("✅ تم استيراد وحدة print_section_monthly بنجاح")
        except ImportError as e:
            print(f"❌ فشل في استيراد وحدة print_section_monthly: {e}")
            return False
        
        # اختبار إنشاء التقرير
        print(f"🚀 بدء إنشاء التقرير المحدث...")
        
        success, output_path, message = print_section_monthly_report(
            parent=None, 
            section=test_section,
            month=test_month
        )
        
        if success:
            print(f"✅ تم إنشاء التقرير المحدث بنجاح!")
            print(f"📁 مسار الملف: {output_path}")
            print(f"💬 الرسالة: {message}")
            
            # التحقق من وجود الملف
            if os.path.exists(output_path):
                file_size = os.path.getsize(output_path)
                print(f"📏 حجم الملف: {file_size} بايت")
                print()
                print("🎉 تم إنشاء التقرير بالتنسيق الجديد!")
                print("📋 يرجى فتح الملف للتحقق من:")
                print("   ✓ ترتيب المعلومات في الجدول الأول")
                print("   ✓ عكس ترتيب الأعمدة في الجدول الثاني")
                print("   ✓ وجود عمود الرقم الترتيبي بدلاً من الملاحظات")
                return True
            else:
                print("❌ الملف غير موجود رغم نجاح العملية")
                return False
        else:
            print(f"❌ فشل في إنشاء التقرير: {message}")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("=" * 70)
    print("🧪 اختبار التقرير المحدث للقسم الشهري")
    print("=" * 70)
    
    result = test_updated_section_report()
    
    print("=" * 70)
    if result:
        print("🎉 الاختبار نجح! التقرير المحدث يعمل بشكل صحيح")
    else:
        print("💥 الاختبار فشل! يحتاج التقرير إلى إصلاح")
    print("=" * 70)
