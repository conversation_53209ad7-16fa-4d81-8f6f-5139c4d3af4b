#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import sys
import sqlite3
import traceback
from datetime import datetime
import subprocess

# إعدادات التقرير السنوي
COL_WIDTHS_TABLE1 = [50, 50, 50, 40]  # جدول معلومات القسم
COL_WIDTHS_TABLE2 = [15, 20, 20, 20, 20, 20, 20, 35, 10]  # جدول الأداءات السنوية (مع الشهر)
COL_WIDTHS_TABLE3 = [60, 60, 70]  # جدول المجاميع

TABLE1_HEADERS = ['القيمة', 'البيان', 'القيمة', 'البيان']
TABLE2_HEADERS = ['الشهر', 'تاريخ الدفع', 'حالة الدفع', 'المتبقي', 'المدفوع', 'المطلوب', 'رمز التلميذ', 'اسم التلميذ', 'الرقم']
TABLE3_HEADERS = ['النسبة المئوية', 'المبلغ', 'البيان']

ROW_HEIGHT_TABLE1 = 8
ROW_HEIGHT_TABLE2 = 7
ROW_HEIGHT_HEADER = 10

PAGE_MARGIN_TOP = 0.2
PAGE_MARGIN_BOTTOM = 0.2
PAGE_MARGIN_LEFT = 10
PAGE_MARGIN_RIGHT = 10

try:
    from fpdf import FPDF
    import arabic_reshaper
    from bidi.algorithm import get_display
except ImportError:
    subprocess.check_call([sys.executable, "-m", "pip", "install", "fpdf2", "arabic-reshaper", "python-bidi"])
    from fpdf import FPDF
    import arabic_reshaper
    from bidi.algorithm import get_display

class ArabicPDF(FPDF):
    def __init__(self):
        super().__init__('P','mm','A4')
        self.set_margins(PAGE_MARGIN_LEFT, PAGE_MARGIN_TOP, PAGE_MARGIN_RIGHT)
        self.set_auto_page_break(auto=True, margin=PAGE_MARGIN_BOTTOM)
        fonts_dir = os.path.join(os.path.dirname(__file__), 'fonts')
        
        # إضافة خطوط Calibri إذا كانت متوفرة
        calibri_path = os.path.join(fonts_dir, 'calibri.ttf')
        calibri_bold_path = os.path.join(fonts_dir, 'calibrib.ttf')
        
        if os.path.exists(calibri_path):
            self.add_font('Calibri', '', calibri_path)
            self.calibri_available = True
        else:
            self.calibri_available = False
            
        if os.path.exists(calibri_bold_path):
            self.add_font('Calibri', 'B', calibri_bold_path)
            self.calibri_bold_available = True
        else:
            self.calibri_bold_available = False
        
        # تعيين الخط الافتراضي
        if self.calibri_available:
            self.set_font('Calibri', '', 13)
        else:
            self.set_font('Arial', '', 13)
        self.set_line_width(0.4)

    def set_title_font(self, size=15):
        """تعيين خط العناوين"""
        if self.calibri_bold_available:
            self.set_font('Calibri', 'B', size)
        else:
            self.set_font('Arial', 'B', size)

    def set_detail_font(self, size=13):
        """تعيين خط التفاصيل"""
        if self.calibri_bold_available:
            self.set_font('Calibri', 'B', size)
        else:
            self.set_font('Arial', 'B', size)

    def set_normal_font(self, size=12):
        """تعيين الخط العادي"""
        if self.calibri_available:
            self.set_font('Calibri', '', size)
        else:
            self.set_font('Arial', '', size)

    def ar_text(self, txt: str) -> str:
        """تحويل النص العربي ليتم عرضه بشكل صحيح"""
        reshaped = arabic_reshaper.reshape(str(txt))
        return get_display(reshaped)

def get_section_info_from_db(db_path, section):
    """جلب معلومات القسم من جدول_المواد_والاقسام"""
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # جلب معلومات القسم من جدول المواد والأقسام
        cursor.execute("""
            SELECT اسم_الاستاذ, المادة, المجموعة, نسبة_الواجبات
            FROM جدول_المواد_والاقسام
            WHERE القسم = ?
            ORDER BY المادة
        """, (section,))
        
        section_subjects = cursor.fetchall()
        
        # جلب إحصائيات التلاميذ في القسم
        cursor.execute("""
            SELECT COUNT(*) as total_students,
                   COUNT(CASE WHEN النوع = 'ذكر' THEN 1 END) as male_count,
                   COUNT(CASE WHEN النوع = 'أنثى' THEN 1 END) as female_count
            FROM جدول_البيانات
            WHERE القسم = ?
        """, (section,))
        
        student_stats = cursor.fetchone()
        
        conn.close()
        
        return {
            'section_subjects': section_subjects,
            'student_stats': student_stats
        }
        
    except Exception as e:
        print(f"خطأ في جلب معلومات القسم: {str(e)}")
        return None

def get_yearly_duties_by_section_year(db_path, section, year):
    """جلب الأداءات السنوية للقسم في السنة المحددة"""
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # ربط جدول monthly_duties مع جدول_البيانات مع الاعتماد على القسم في monthly_duties وإضافة اسم الأستاذ
        cursor.execute("""
            SELECT
                jb.اسم_التلميذ,
                jb.رمز_التلميذ,
                md.amount_required,
                md.amount_paid,
                md.amount_remaining,
                md.payment_status,
                md.payment_date,
                md.notes,
                md.month,
                md.اسم_الاستاذ
            FROM monthly_duties md
            JOIN جدول_البيانات jb ON md.student_id = jb.id
            WHERE md.القسم = ? AND md.year = ?
            ORDER BY jb.اسم_التلميذ, md.month
        """, (section, year))
        
        yearly_duties = cursor.fetchall()
        conn.close()
        
        return yearly_duties
        
    except Exception as e:
        print(f"خطأ في جلب الأداءات السنوية: {str(e)}")
        return []

def get_monthly_summary_by_section_year(db_path, section, year):
    """جلب ملخص شهري للقسم في السنة المحددة"""
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # جلب ملخص شهري مع اسم الأستاذ
        cursor.execute("""
            SELECT
                md.month,
                md.اسم_الاستاذ,
                COUNT(*) as student_count,
                SUM(md.amount_required) as total_required,
                SUM(md.amount_paid) as total_paid,
                SUM(md.amount_remaining) as total_remaining
            FROM monthly_duties md
            JOIN جدول_البيانات jb ON md.student_id = jb.id
            WHERE md.القسم = ? AND md.year = ?
            GROUP BY md.month, md.اسم_الاستاذ
            ORDER BY 
                CASE md.month
                    WHEN 'يناير' THEN 1
                    WHEN 'فبراير' THEN 2
                    WHEN 'مارس' THEN 3
                    WHEN 'أبريل' THEN 4
                    WHEN 'مايو' THEN 5
                    WHEN 'يونيو' THEN 6
                    WHEN 'يوليو' THEN 7
                    WHEN 'أغسطس' THEN 8
                    WHEN 'سبتمبر' THEN 9
                    WHEN 'أكتوبر' THEN 10
                    WHEN 'نوفمبر' THEN 11
                    WHEN 'ديسمبر' THEN 12
                    ELSE 13
                END
        """, (section, year))
        
        monthly_summary = cursor.fetchall()
        conn.close()
        
        return monthly_summary

    except Exception as e:
        print(f"خطأ في جلب الملخص الشهري: {str(e)}")
        return []

def generate_section_yearly_report(logo_path, section_info, yearly_duties, monthly_summary, section, year, output_path):
    """إنشاء تقرير القسم السنوي"""
    pdf = ArabicPDF()
    margin = 10
    usable_w = pdf.w - 2 * margin

    pdf.add_page()
    y = pdf.get_y()

    # إضافة الشعار إذا كان متوفراً
    if logo_path and os.path.exists(logo_path):
        logo_w, logo_h = 60, 30
        x_logo = (pdf.w - logo_w) / 2
        pdf.image(logo_path, x=x_logo, y=y, w=logo_w, h=logo_h)
        y += logo_h + 5

    # عنوان التقرير
    pdf.set_draw_color(0, 51, 102)
    pdf.set_line_width(0.5)
    FIXED_BOX_HEIGHT = 12

    title_text = f"تقرير القسم السنوي - القسم: {section} - السنة: {year}"

    pdf.set_text_color(0, 51, 102)
    pdf.set_xy(margin, y)
    pdf.set_title_font(15)
    pdf.cell(usable_w, FIXED_BOX_HEIGHT, pdf.ar_text(title_text), border=1, align='C')

    pdf.set_text_color(0, 0, 0)
    y += FIXED_BOX_HEIGHT + 5

    # الجدول الأول: معلومات القسم
    pdf.set_title_font(14)
    pdf.set_text_color(0, 100, 0)
    pdf.set_xy(margin, y)
    pdf.cell(usable_w, 8, pdf.ar_text('معلومات القسم والمواد'), border=0, align='C')
    pdf.set_text_color(0, 0, 0)
    y += 10

    cols1 = COL_WIDTHS_TABLE1

    # معلومات إحصائية عن القسم
    if section_info and section_info['student_stats']:
        stats = section_info['student_stats']

        # إضافة معلومات المواد
        if section_info['section_subjects']:
            first_subject = section_info['section_subjects'][0]
            teacher_name = first_subject[0] or 'غير محدد'
            subject_name = first_subject[1] or 'غير محدد'
            group_name = first_subject[2] or 'غير محدد'
            duties_percent = str(first_subject[3]) + '%' if first_subject[3] else '100%'

            # العمود الرابع والثاني في نفس الصفوف
            section_info_rows = [
                [subject_name, 'المادة', str(stats[0]), 'إجمالي التلاميذ'],
                [teacher_name, 'الأستاذ(ة)', str(stats[1]), 'عدد الذكور'],
                [year, 'السنة المحددة', str(stats[2]), 'عدد الإناث'],
                [duties_percent, 'حصة الأستاذ(ة) من المبلغ', group_name, 'المجموعة']
            ]

        else:
            # معلومات افتراضية
            section_info_rows = [
                ['غير محدد', 'المادة', str(stats[0]), 'إجمالي التلاميذ'],
                ['غير محدد', 'الأستاذ(ة)', str(stats[1]), 'عدد الذكور'],
                [year, 'السنة المحددة', str(stats[2]), 'عدد الإناث'],
                ['100%', 'حصة الأستاذ(ة) من المبلغ', 'غير محدد', 'المجموعة']
            ]
    else:
        section_info_rows = [
            ['غير محدد', 'المادة', '0', 'إجمالي التلاميذ'],
            ['غير محدد', 'الأستاذ(ة)', '0', 'عدد الذكور'],
            [year, 'السنة المحددة', '0', 'عدد الإناث'],
            ['100%', 'حصة الأستاذ(ة) من المبلغ', 'غير محدد', 'المجموعة']
        ]

    pdf.set_detail_font(13)
    pdf.set_fill_color(230, 240, 255)

    # رسم صفوف معلومات القسم
    for row in section_info_rows:
        x = margin
        for i, cell in enumerate(row):
            pdf.set_xy(x, y)
            fill = i % 2 == 1
            align = 'C' if i % 2 == 1 else 'R'
            pdf.cell(cols1[i], ROW_HEIGHT_TABLE1, pdf.ar_text(cell), border=1, align=align, fill=fill)
            x += cols1[i]
        y += ROW_HEIGHT_TABLE1

    y += 10

    # جدول الملخص الشهري
    pdf.set_title_font(14)
    pdf.set_text_color(128, 0, 128)
    pdf.set_xy(margin, y)
    pdf.cell(usable_w, 8, pdf.ar_text(f'الملخص الشهري - {year}'), border=0, align='C')
    pdf.set_text_color(0, 0, 0)
    y += 10

    # رسم جدول الملخص الشهري مع إضافة عمود اسم الأستاذ
    monthly_cols = [25, 30, 20, 30, 30, 30, 25]  # عرض الأعمدة
    monthly_headers = ['الشهر', 'اسم الأستاذ', 'عدد التلاميذ', 'المطلوب', 'المدفوع', 'المتبقي', 'نسبة التحصيل']

    pdf.set_detail_font(12)
    pdf.set_fill_color(255, 200, 255)

    # رسم رأس جدول الملخص الشهري
    x = margin
    for i, header in enumerate(monthly_headers):
        pdf.set_xy(x, y)
        pdf.cell(monthly_cols[i], ROW_HEIGHT_HEADER, pdf.ar_text(header), border=1, align='C', fill=True)
        x += monthly_cols[i]

    y += ROW_HEIGHT_HEADER
    pdf.set_normal_font(10)

    # محتوى جدول الملخص الشهري مع اسم الأستاذ
    if monthly_summary:
        for summary in monthly_summary:
            x = margin
            month = summary[0]
            teacher_name = summary[1] or 'غير محدد'
            student_count = summary[2]
            total_required = float(summary[3]) if summary[3] else 0
            total_paid = float(summary[4]) if summary[4] else 0
            total_remaining = float(summary[5]) if summary[5] else 0

            # حساب نسبة التحصيل
            collection_rate = (total_paid / total_required * 100) if total_required > 0 else 0

            data = [
                month,
                teacher_name,
                str(student_count),
                f'{total_required:.2f}',
                f'{total_paid:.2f}',
                f'{total_remaining:.2f}',
                f'{collection_rate:.1f}%'
            ]

            # تلوين حسب نسبة التحصيل
            if collection_rate >= 90:
                pdf.set_fill_color(220, 255, 220)  # أخضر فاتح
            elif collection_rate >= 70:
                pdf.set_fill_color(255, 255, 200)  # أصفر فاتح
            else:
                pdf.set_fill_color(255, 220, 220)  # أحمر فاتح

            for j, cell in enumerate(data):
                pdf.set_xy(x, y)
                pdf.cell(monthly_cols[j], ROW_HEIGHT_TABLE2, pdf.ar_text(cell), border=1, align='C', fill=True)
                x += monthly_cols[j]

            y += ROW_HEIGHT_TABLE2

            # الانتقال إلى صفحة جديدة عند الحاجة
            if y > pdf.h - 50:
                pdf.add_page()
                y = pdf.get_y()
    else:
        # إذا لم توجد أداءات سنوية
        pdf.set_xy(margin, y)
        pdf.set_fill_color(255, 245, 245)
        pdf.cell(sum(monthly_cols), ROW_HEIGHT_TABLE2, pdf.ar_text('لا توجد أداءات مسجلة لهذا القسم في هذه السنة'), border=1, align='C', fill=True)
        y += ROW_HEIGHT_TABLE2

    y += 10

    # الجدول الثالث: مجموع المبالغ السنوية وحصة الأستاذ
    pdf.set_title_font(14)
    pdf.set_text_color(0, 100, 100)
    pdf.set_xy(margin, y)
    pdf.cell(usable_w, 8, pdf.ar_text('مجموع المبالغ السنوية وحصة الأستاذ(ة)'), border=0, align='C')
    pdf.set_text_color(0, 0, 0)
    y += 10

    cols3 = COL_WIDTHS_TABLE3

    # حساب المجاميع السنوية (مع تعديل الفهارس بعد إضافة عمود اسم الأستاذ)
    total_required_year = 0
    total_paid_year = 0
    total_remaining_year = 0

    if monthly_summary:
        for summary in monthly_summary:
            total_required_year += float(summary[3]) if summary[3] else 0
            total_paid_year += float(summary[4]) if summary[4] else 0
            total_remaining_year += float(summary[5]) if summary[5] else 0

    # حساب حصة الأستاذ
    teacher_percentage = 100  # افتراضي
    if section_info and section_info['section_subjects']:
        teacher_percentage = section_info['section_subjects'][0][3] if section_info['section_subjects'][0][3] else 100

    teacher_share_year = (total_paid_year * teacher_percentage) / 100

    pdf.set_detail_font(13)
    pdf.set_fill_color(200, 255, 200)

    # رسم رأس الجدول الثالث
    x = margin
    for i, header in enumerate(TABLE3_HEADERS):
        pdf.set_xy(x, y)
        pdf.cell(cols3[i], ROW_HEIGHT_HEADER, pdf.ar_text(header), border=1, align='C', fill=True)
        x += cols3[i]

    y += ROW_HEIGHT_HEADER
    pdf.set_normal_font(12)

    # محتوى جدول المجاميع السنوية
    summary_data = [
        ['-', f'{total_required_year:.2f}', 'إجمالي المبلغ المطلوب (سنوي)'],
        ['-', f'{total_paid_year:.2f}', 'إجمالي المبلغ المحصل (سنوي)'],
        ['-', f'{total_remaining_year:.2f}', 'إجمالي المبلغ المتبقي (سنوي)'],
        [f'{teacher_percentage}%', f'{teacher_share_year:.2f}', 'حصة الأستاذ(ة) من المبلغ المحصل (سنوي)']
    ]

    for i, row in enumerate(summary_data):
        x = margin

        # تلوين مختلف لكل صف
        if i == 0:  # المطلوب
            pdf.set_fill_color(255, 240, 240)  # أحمر فاتح
        elif i == 1:  # المحصل
            pdf.set_fill_color(240, 255, 240)  # أخضر فاتح
        elif i == 2:  # المتبقي
            pdf.set_fill_color(255, 255, 240)  # أصفر فاتح
        else:  # حصة الأستاذ
            pdf.set_fill_color(240, 240, 255)  # أزرق فاتح

        for j, cell in enumerate(row):
            pdf.set_xy(x, y)
            pdf.cell(cols3[j], ROW_HEIGHT_TABLE2, pdf.ar_text(cell), border=1, align='C', fill=True)
            x += cols3[j]

        y += ROW_HEIGHT_TABLE2

    # إضافة تاريخ الطباعة
    y += 10
    current_date = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    pdf.set_normal_font(10)
    pdf.set_xy(margin, y)
    pdf.cell(usable_w/2, 8, pdf.ar_text(f'تاريخ الطباعة: {current_date}'), border=0, align='R')

    os.makedirs(os.path.dirname(output_path), exist_ok=True)
    pdf.output(output_path)
    print(f"تم إنشاء تقرير القسم السنوي: {output_path}")

def print_section_yearly_report(parent=None, section=None, year=None):
    """
    دالة لإنشاء تقرير القسم السنوي

    المعاملات:
        parent: كائن النافذة الأم
        section: اسم القسم
        year: السنة

    العوائد:
        (success, output_path, message): ثلاثية تحدد نجاح العملية ومسار الملف ورسالة النتيجة
    """
    try:
        if not section:
            return False, None, "اسم القسم غير محدد."

        if not year:
            return False, None, "السنة غير محددة."

        # تحديد مسار قاعدة البيانات
        db_path = os.path.join(os.path.dirname(__file__), 'data.db')

        # جلب بيانات القسم والأداءات السنوية
        try:
            # جلب شعار المؤسسة
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()

            cursor.execute("SELECT ImagePath1 FROM بيانات_المؤسسة LIMIT 1")
            logo_row = cursor.fetchone()
            logo_path = logo_row[0] if logo_row and os.path.exists(logo_row[0]) else None

            conn.close()

            # جلب معلومات القسم
            section_info = get_section_info_from_db(db_path, section)

            # جلب الأداءات السنوية
            yearly_duties = get_yearly_duties_by_section_year(db_path, section, year)

            # جلب الملخص الشهري
            monthly_summary = get_monthly_summary_by_section_year(db_path, section, year)

        except Exception as db_error:
            print(f"خطأ في الوصول لقاعدة البيانات: {db_error}")
            return False, None, f"خطأ في الوصول لقاعدة البيانات: {str(db_error)}"

        # إنشاء مجلد التقارير إذا لم يكن موجوداً
        reports_dir = os.path.join(os.path.expanduser('~'), 'Desktop', 'تقارير الأقسام السنوية')
        if not os.path.exists(reports_dir):
            os.makedirs(reports_dir)

        # تحديد اسم الملف
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        section_clean = section.replace(' ', '_').replace('/', '_')
        output_path = os.path.join(reports_dir, f"تقرير_القسم_السنوي_{section_clean}_{year}_{timestamp}.pdf")

        # إنشاء التقرير
        generate_section_yearly_report(logo_path, section_info, yearly_duties, monthly_summary, section, year, output_path)

        # فتح الملف بعد إنشائه
        try:
            if sys.platform == 'win32':
                os.startfile(output_path)
            elif sys.platform == 'darwin':  # macOS
                subprocess.call(['open', output_path])
            else:  # Linux
                subprocess.call(['xdg-open', output_path])
        except Exception as e:
            return True, output_path, f"تم إنشاء التقرير ولكن تعذر فتح الملف: {str(e)}"

        return True, output_path, "تم إنشاء تقرير القسم السنوي بنجاح."

    except Exception as e:
        traceback.print_exc()
        return False, None, f"حدث خطأ في إنشاء تقرير القسم السنوي: {str(e)}"

if __name__=='__main__':
    # مثال على الاستخدام
    try:
        success, output_path, message = print_section_yearly_report(
            section="قسم / 01",
            year="2024"
        )

        print(f"النتيجة: {success}")
        print(f"المسار: {output_path}")
        print(f"الرسالة: {message}")

    except Exception as e:
        print(f"خطأ في الاختبار: {e}")
