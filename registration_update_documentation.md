# توثيق التحديثات على وظيفة التسجيل وإعادة التسجيل

## الوصف
تم تطوير وظيفة زر "التسجيل وإعادة التسجيل" في ملف `sub252_window_backup.py` لتدعم ثلاث حالات مختلفة بدلاً من حالتين فقط.

## الحالات المدعومة

### 1️⃣ الحالة الأولى: إضافة تلميذ جديد
- **الشرط:** عدم تحديد أي تلميذ من الجدول
- **الإجراء:** فتح نافذة التسجيل في وضع الإضافة
- **المميزات:**
  - إنشاء رمز تلميذ تلقائي (P10000، P10001، إلخ...)
  - مسح جميع الحقول للبدء من جديد
  - عنوان النافذة: "إضافة تلميذ جديد"

### 2️⃣ الحالة الثانية: تعديل بيانات تلميذ موجود
- **الشرط:** تحديد تلميذ واحد من الجدول واختيار "تعديل البيانات الحالية"
- **الإجراء:** فتح نافذة التسجيل في وضع التعديل
- **المميزات:**
  - تحميل جميع البيانات الحالية للتلميذ
  - إمكانية تعديل أي معلومة
  - عنوان النافذة: "تعديل بيانات التلميذ - ID: {student_id}"

### 3️⃣ الحالة الثالثة: إضافة قسم جديد لتلميذ موجود ⭐ **جديد**
- **الشرط:** تحديد تلميذ واحد من الجدول واختيار "إضافة قسم جديد"
- **الإجراء:** فتح نافذة التسجيل لإنشاء سجل جديد بمعلومات التلميذ الأساسية
- **المميزات:**
  - تعبئة تلقائية للمعلومات الشخصية (الاسم، الرمز، النوع، الهواتف)
  - إمكانية اختيار مجموعة وقسم مختلفين
  - إمكانية تعديل الواجبات المالية حسب القسم الجديد
  - عنوان النافذة: "إضافة قسم جديد للتلميذ: {student_name}"

## آلية العمل

### عند النقر على زر "التسجيل وإعادة التسجيل":

1. **فحص التحديد:**
   ```python
   selected_ids = self.get_selected_student_id()
   ```

2. **تحديد السيناريو:**
   - إذا كان `len(selected_ids) == 0`: الحالة الأولى
   - إذا كان `len(selected_ids) == 1`: عرض حوار الاختيار
   - إذا كان `len(selected_ids) > 1`: رسالة تحذير

3. **حوار الاختيار (للحالة الثانية والثالثة):**
   - زر "✏️ تعديل البيانات الحالية" → الحالة الثانية
   - زر "➕ إضافة قسم جديد" → الحالة الثالثة
   - زر "❌ إلغاء" → إغلاق الحوار

## الوظائف الجديدة المضافة

### `handle_registration()`
الوظيفة الرئيسية التي تحدد نوع العملية وتعرض حوار الاختيار.

### `handle_edit_choice(dialog, student_id)`
تتعامل مع اختيار تعديل البيانات الحالية.

### `handle_add_section_choice(dialog, student_id)`
تتعامل مع اختيار إضافة قسم جديد.

### `get_student_info(student_id)`
تحصل على المعلومات الأساسية للتلميذ من قاعدة البيانات.

### `open_registration_window_add_section_mode(student_id)`
تفتح نافذة التسجيل في وضع إضافة قسم جديد مع تعبئة المعلومات الأساسية.

## الفوائد الجديدة

1. **مرونة أكبر:** يمكن للتلميذ الواحد التسجيل في أقسام متعددة
2. **توفير الوقت:** لا حاجة لإعادة إدخال المعلومات الشخصية
3. **تنظيم أفضل:** كل قسم له سجل منفصل مع واجبات مالية مستقلة
4. **سهولة الاستخدام:** حوار بسيط وواضح للاختيار

## مثال على الاستخدام

1. التلميذ "أحمد محمد" مسجل في قسم "الرياضيات"
2. يريد التسجيل أيضاً في قسم "الفيزياء"
3. نحدد سجل أحمد من الجدول
4. ننقر على زر "التسجيل وإعادة التسجيل"
5. نختار "➕ إضافة قسم جديد"
6. تفتح النافذة مع معلومات أحمد الشخصية معبأة مسبقاً
7. نختار قسم "الفيزياء" ونعبئ الواجبات المالية الجديدة
8. نحفظ البيانات
9. النتيجة: سجلان منفصلان لأحمد (رياضيات + فيزياء)

## ملاحظات فنية

- يتم حفظ `original_student_id` للمرجعية لكن `current_student_id` يبقى `None` لضمان إنشاء سجل جديد
- المعلومات الشخصية (الاسم، الرمز، النوع، الهواتف) تُنسخ تلقائياً
- المعلومات الأكاديمية والمالية تُترك فارغة للتعبئة حسب القسم الجديد
- كل سجل له `id` منفصل ويمكن إدارته بشكل مستقل

## الملفات المُعدَّلة

- `sub252_window_backup.py`: الملف الرئيسي مع جميع التحديثات
- `test_registration_update.py`: ملف اختبار الوظائف الجديدة

---

**تاريخ التحديث:** ديسمبر 2024  
**المطور:** GitHub Copilot  
**الحالة:** ✅ مكتمل ومُختبر
