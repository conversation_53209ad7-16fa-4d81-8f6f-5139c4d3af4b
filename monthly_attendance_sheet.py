#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import sqlite3
import calendar
from datetime import datetime, timedelta
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, 
                             QHBoxLayout, QPushButton, QLabel, QComboBox, 
                             QMessageBox, QDialog, QTextEdit, QGroupBox)
from PyQt5.QtCore import Qt, QDate
from PyQt5.QtGui import QFont, QTextDocument, QTextCursor
from PyQt5.QtPrintSupport import QPrinter, QPrintDialog

class MonthlyAttendanceSheet(QMainWindow):
    def __init__(self, db_path="data.db", parent=None):
        super().__init__(parent)
        self.db_path = db_path
        self.init_ui()
        self.load_sections()

    def init_ui(self):
        """إنشاء واجهة المستخدم"""
        self.setWindowTitle("ورقة متابعة الغياب الشهرية")
        self.setGeometry(100, 100, 800, 600)
        
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        main_layout = QVBoxLayout(central_widget)
        
        # العنوان الرئيسي
        title_label = QLabel("📋 ورقة متابعة الغياب الشهرية")
        title_label.setFont(QFont("Calibri", 15, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                background-color: #2c3e50;
                color: white;
                padding: 15px;
                border-radius: 8px;
                margin-bottom: 10px;
            }
        """)
        main_layout.addWidget(title_label)
        
        # مجموعة الإعدادات
        settings_group = QGroupBox("⚙️ إعدادات الورقة")
        settings_group.setFont(QFont("Calibri", 14, QFont.Bold))
        settings_layout = QHBoxLayout(settings_group)
        
        # اختيار القسم
        section_label = QLabel("القسم:")
        section_label.setFont(QFont("Calibri", 14, QFont.Bold))
        settings_layout.addWidget(section_label)
        
        self.section_combo = QComboBox()
        self.section_combo.setFont(QFont("Calibri", 14, QFont.Bold))
        settings_layout.addWidget(self.section_combo)
        
        # اختيار الشهر
        month_label = QLabel("الشهر:")
        month_label.setFont(QFont("Calibri", 14, QFont.Bold))
        settings_layout.addWidget(month_label)
        
        self.month_combo = QComboBox()
        self.month_combo.setFont(QFont("Calibri", 14, QFont.Bold))
        months = ["يناير", "فبراير", "مارس", "أبريل", "مايو", "يونيو",
                  "يوليو", "أغسطس", "سبتمبر", "أكتوبر", "نوفمبر", "ديسمبر"]
        self.month_combo.addItems(months)
        self.month_combo.setCurrentIndex(datetime.now().month - 1)
        settings_layout.addWidget(self.month_combo)
        
        # اختيار السنة
        year_label = QLabel("السنة:")
        year_label.setFont(QFont("Calibri", 14, QFont.Bold))
        settings_layout.addWidget(year_label)
        
        self.year_combo = QComboBox()
        self.year_combo.setFont(QFont("Calibri", 14, QFont.Bold))
        current_year = datetime.now().year
        for year in range(current_year - 2, current_year + 3):
            self.year_combo.addItem(str(year))
        self.year_combo.setCurrentText(str(current_year))
        settings_layout.addWidget(self.year_combo)
        
        main_layout.addWidget(settings_group)
        
        # أزرار العمليات
        buttons_layout = QHBoxLayout()
        
        # زر إنشاء الورقة
        generate_btn = QPushButton("📄 إنشاء ورقة PDF")
        generate_btn.setFont(QFont("Calibri", 14, QFont.Bold))
        generate_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border-radius: 6px;
                padding: 10px 20px;
            }
            QPushButton:hover {
                background-color: #219a52;
            }
        """)
        generate_btn.clicked.connect(self.generate_pdf_report)
        buttons_layout.addWidget(generate_btn)
        
        # زر معاينة
        preview_btn = QPushButton("👁️ معاينة")
        preview_btn.setFont(QFont("Calibri", 14, QFont.Bold))
        preview_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border-radius: 6px;
                padding: 10px 20px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        preview_btn.clicked.connect(self.preview_sheet)
        buttons_layout.addWidget(preview_btn)
        
        # زر طباعة
        print_btn = QPushButton("🖨️ طباعة")
        print_btn.setFont(QFont("Calibri", 14, QFont.Bold))
        print_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border-radius: 6px;
                padding: 10px 20px;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        print_btn.clicked.connect(self.print_sheet)
        buttons_layout.addWidget(print_btn)
        
        main_layout.addLayout(buttons_layout)

        # منطقة المعاينة
        self.preview_text = QTextEdit()
        self.preview_text.setFont(QFont("Calibri", 12))
        self.preview_text.setReadOnly(True)
        self.preview_text.setPlaceholderText("ستظهر معاينة الورقة هنا...")
        main_layout.addWidget(self.preview_text)

    def generate_pdf_report(self):
        """إنشاء تقرير PDF وفتحه"""
        try:
            selected_section = self.section_combo.currentText()
            if not selected_section:
                QMessageBox.warning(self, "تحذير", "يرجى اختيار قسم")
                return

            month_index = self.month_combo.currentIndex() + 1
            year = int(self.year_combo.currentText())
            month_name = self.month_combo.currentText()

            print(f"🔧 إنشاء تقرير PDF للقسم: {selected_section}")

            # استيراد وتشغيل نظام التقارير
            from attendance_sheet_report import create_attendance_sheet_report

            success, output_path, message = create_attendance_sheet_report(
                selected_section, year, month_index, month_name, self.db_path
            )

            if success:
                QMessageBox.information(self, "نجح", f"تم إنشاء التقرير بنجاح!\n\nالمسار: {output_path}")
                # تحديث المعاينة
                self.preview_text.setPlainText(f"تم إنشاء تقرير PDF بنجاح:\n\n{output_path}\n\nتم فتح الملف تلقائياً.")
            else:
                QMessageBox.critical(self, "خطأ", message)

        except Exception as e:
            error_msg = f"خطأ في إنشاء التقرير: {str(e)}"
            print(f"❌ {error_msg}")
            QMessageBox.critical(self, "خطأ", error_msg)

    def load_sections(self):
        """تحميل الأقسام"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            self.section_combo.clear()
            
            # جلب الأقسام من جدول المواد والأقسام
            cursor.execute("SELECT DISTINCT القسم FROM جدول_المواد_والاقسام WHERE القسم IS NOT NULL AND القسم != '' ORDER BY القسم")
            sections = cursor.fetchall()
            
            for section in sections:
                if section[0]:
                    self.section_combo.addItem(section[0])
            
            conn.close()
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تحميل الأقسام: {str(e)}")

    def get_weeks_in_month(self, year, month):
        """حساب الأسابيع في الشهر (الاثنين بداية الأسبوع)"""
        # أول يوم في الشهر
        first_day = datetime(year, month, 1)
        # آخر يوم في الشهر
        last_day = datetime(year, month, calendar.monthrange(year, month)[1])
        
        # العثور على أول اثنين
        days_until_monday = (7 - first_day.weekday()) % 7
        if first_day.weekday() != 0:  # إذا لم يكن اثنين
            first_monday = first_day + timedelta(days=days_until_monday)
        else:
            first_monday = first_day
        
        weeks = []
        current_monday = first_monday
        
        while current_monday <= last_day:
            week_end = current_monday + timedelta(days=6)  # الأحد
            if week_end > last_day:
                week_end = last_day
            
            weeks.append({
                'start': current_monday,
                'end': week_end,
                'week_num': len(weeks) + 1
            })
            
            current_monday += timedelta(days=7)
        
        return weeks

    def generate_sheet(self):
        """إنشاء ورقة متابعة الغياب"""
        try:
            print("🔧 بدء إنشاء ورقة متابعة الغياب...")

            selected_section = self.section_combo.currentText()
            if not selected_section:
                QMessageBox.warning(self, "تحذير", "يرجى اختيار قسم")
                return

            month_index = self.month_combo.currentIndex() + 1
            year = int(self.year_combo.currentText())
            month_name = self.month_combo.currentText()

            print(f"📋 المعاملات: القسم={selected_section}, الشهر={month_name}, السنة={year}")

            # التحقق من صحة البيانات
            if month_index < 1 or month_index > 12:
                raise Exception(f"رقم الشهر غير صحيح: {month_index}")

            if year < 2020 or year > 2030:
                raise Exception(f"السنة غير صحيحة: {year}")

            # إنشاء محتوى الورقة
            print("📄 إنشاء محتوى الورقة...")
            html_content = self.create_sheet_html(selected_section, year, month_index, month_name)

            if not html_content or len(html_content.strip()) == 0:
                raise Exception("فشل في إنشاء محتوى الورقة - المحتوى فارغ")

            # عرض في منطقة المعاينة
            print("👁️ عرض المعاينة...")
            self.preview_text.setHtml(html_content)

            print("✅ تم إنشاء ورقة متابعة الغياب بنجاح")
            QMessageBox.information(self, "نجح", "تم إنشاء ورقة متابعة الغياب بنجاح")

        except Exception as e:
            error_msg = f"خطأ في إنشاء الورقة: {str(e)}"
            print(f"❌ {error_msg}")
            QMessageBox.critical(self, "خطأ", error_msg)
            import traceback
            traceback.print_exc()

    def create_sheet_html(self, section, year, month, month_name):
        """إنشاء محتوى HTML للورقة"""
        try:
            print(f"🔧 بدء إنشاء ورقة الغياب للقسم: {section}")
            print(f"📅 الشهر: {month_name} {year}")

            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # جلب معلومات القسم
            print("📊 جلب معلومات القسم...")
            try:
                cursor.execute("""
                    SELECT القسم, المادة, اسم_الاستاذ, عدد_التلاميذ
                    FROM جدول_المواد_والاقسام
                    WHERE القسم = ?
                    LIMIT 1
                """, (section,))

                section_info = cursor.fetchone()
                if not section_info:
                    print("⚠️ لم يتم العثور على معلومات القسم في جدول_المواد_والاقسام")
                    # محاولة جلب من جدول البيانات
                    cursor.execute("SELECT DISTINCT القسم FROM جدول_البيانات WHERE القسم = ?", (section,))
                    if cursor.fetchone():
                        section_info = (section, "غير محدد", "غير محدد", 0)
                    else:
                        raise Exception(f"القسم '{section}' غير موجود في قاعدة البيانات")
                else:
                    print(f"✅ تم جلب معلومات القسم: {section_info}")
            except Exception as e:
                print(f"❌ خطأ في جلب معلومات القسم: {e}")
                section_info = (section, "غير محدد", "غير محدد", 0)

            # جلب التلاميذ
            print("👥 جلب قائمة التلاميذ...")
            try:
                cursor.execute("""
                    SELECT id, اسم_التلميذ, رمز_التلميذ
                    FROM جدول_البيانات
                    WHERE القسم = ? AND اسم_التلميذ IS NOT NULL AND اسم_التلميذ != ''
                    ORDER BY اسم_التلميذ
                """, (section,))

                students = cursor.fetchall()
                print(f"✅ تم جلب {len(students)} تلميذ")

                if len(students) == 0:
                    print("⚠️ لا يوجد تلاميذ في هذا القسم")
                    # جلب عينة للتشخيص
                    cursor.execute("SELECT DISTINCT القسم FROM جدول_البيانات WHERE القسم IS NOT NULL LIMIT 10")
                    available_sections = cursor.fetchall()
                    print(f"📋 الأقسام المتاحة: {available_sections}")

            except Exception as e:
                print(f"❌ خطأ في جلب التلاميذ: {e}")
                students = []

            # حساب الأسابيع
            print("📅 حساب أسابيع الشهر...")
            try:
                weeks = self.get_weeks_in_month(year, month)
                print(f"✅ تم حساب {len(weeks)} أسبوع")
                for i, week in enumerate(weeks):
                    print(f"   الأسبوع {i+1}: {week['start'].strftime('%Y-%m-%d')} إلى {week['end'].strftime('%Y-%m-%d')}")
            except Exception as e:
                print(f"❌ خطأ في حساب الأسابيع: {e}")
                weeks = []

            conn.close()
            
            # التحقق من وجود بيانات كافية
            if not students:
                raise Exception(f"لا يوجد تلاميذ في القسم '{section}'. تأكد من وجود تلاميذ مسجلين في هذا القسم.")

            if not weeks:
                raise Exception(f"خطأ في حساب أسابيع الشهر {month_name} {year}")

            # بناء HTML
            print("🔧 بناء محتوى HTML...")
            try:
                html = f"""
                <!DOCTYPE html>
                <html dir="rtl">
                <head>
                    <meta charset="UTF-8">
                    <style>
                        body {{ font-family: 'Arial', sans-serif; margin: 20px; }}
                        .header {{ text-align: center; margin-bottom: 20px; }}
                        .logo {{ font-size: 24px; font-weight: bold; color: #2c3e50; }}
                        .title {{ font-size: 18px; font-weight: bold; margin: 10px 0; }}
                        .section-info {{ border: 2px solid #34495e; padding: 10px; margin: 15px 0; }}
                        .section-info table {{ width: 100%; border-collapse: collapse; }}
                        .section-info td {{ padding: 5px; border: 1px solid #bdc3c7; }}
                        .attendance-table {{ width: 100%; border-collapse: collapse; margin-top: 20px; }}
                        .attendance-table th, .attendance-table td {{
                            border: 1px solid #000;
                            padding: 4px;
                            text-align: center;
                            font-size: 10px;
                        }}
                        .attendance-table th {{ background-color: #ecf0f1; font-weight: bold; }}
                        .week-header {{ background-color: #3498db; color: white; }}
                        .session-cell {{ width: 25px; }}
                    </style>
                </head>
                <body>
                    <div class="header">
                        <div class="logo">🏫 مؤسسة التعليم</div>
                        <div class="title">ورقة متابعة الغياب الشهرية</div>
                        <div>شهر {month_name} {year}</div>
                    </div>

                    <div class="section-info">
                        <table>
                            <tr>
                                <td><strong>القسم:</strong> {section_info[0]}</td>
                                <td><strong>المادة:</strong> {section_info[1]}</td>
                            </tr>
                            <tr>
                                <td><strong>الأستاذ(ة):</strong> {section_info[2]}</td>
                                <td><strong>عدد التلاميذ:</strong> {len(students)}</td>
                            </tr>
                        </table>
                    </div>

                    <table class="attendance-table">
                        <thead>
                            <tr>
                                <th rowspan="2">رقم الترتيب</th>
                                <th rowspan="2">الاسم والنسب</th>
                                <th rowspan="2">ID</th>
                """
            except Exception as e:
                raise Exception(f"خطأ في بناء رأس HTML: {str(e)}")
            
            # إضافة رؤوس الأسابيع
            try:
                for week in weeks:
                    html += f'<th colspan="3" class="week-header">الأسبوع {week["week_num"]}</th>'

                html += """
                            </tr>
                            <tr>
                """

                # إضافة رؤوس الحصص
                for week in weeks:
                    html += '<th class="session-cell">ح1</th><th class="session-cell">ح2</th><th class="session-cell">ح3</th>'

                html += """
                            </tr>
                        </thead>
                        <tbody>
                """
                print("✅ تم بناء رؤوس الجدول")
            except Exception as e:
                raise Exception(f"خطأ في بناء رؤوس الجدول: {str(e)}")

            # إضافة صفوف التلاميذ
            try:
                print(f"👥 إضافة {len(students)} تلميذ للجدول...")
                for i, student in enumerate(students, 1):
                    student_id, student_name, student_code = student

                    # التأكد من وجود البيانات
                    safe_name = str(student_name or f"تلميذ {i}")
                    safe_code = str(student_code or student_id or i)

                    html += f"""
                            <tr>
                                <td>{i}</td>
                                <td style="text-align: right; padding-right: 5px;">{safe_name}</td>
                                <td>{safe_code}</td>
                    """

                    # إضافة خلايا الحصص لكل أسبوع
                    for week in weeks:
                        html += '<td class="session-cell"></td><td class="session-cell"></td><td class="session-cell"></td>'

                    html += '</tr>'

                print("✅ تم إضافة جميع التلاميذ")
            except Exception as e:
                raise Exception(f"خطأ في إضافة صفوف التلاميذ: {str(e)}")

            # إنهاء HTML
            try:
                html += """
                        </tbody>
                    </table>

                    <div style="margin-top: 30px; font-size: 12px;">
                        <p><strong>ملاحظات:</strong></p>
                        <p>• يتم وضع علامة (×) في الخانة المقابلة للحصة التي غاب فيها التلميذ</p>
                        <p>• ح1: الحصة الأولى، ح2: الحصة الثانية، ح3: الحصة الثالثة</p>
                        <p>• يبدأ الأسبوع من يوم الاثنين وينتهي يوم الأحد</p>
                    </div>

                    <div style="margin-top: 20px; text-align: left;">
                        <p>التوقيع: ________________</p>
                        <p>التاريخ: ________________</p>
                    </div>
                </body>
                </html>
                """
                print("✅ تم إنهاء بناء HTML بنجاح")
            except Exception as e:
                raise Exception(f"خطأ في إنهاء HTML: {str(e)}")

            return html
            
        except Exception as e:
            error_msg = f"خطأ في إنشاء محتوى الورقة: {str(e)}"
            print(f"❌ {error_msg}")
            import traceback
            traceback.print_exc()
            raise Exception(error_msg)

    def preview_sheet(self):
        """معاينة الورقة"""
        self.generate_sheet()

    def print_sheet(self):
        """طباعة الورقة"""
        if not self.preview_text.toHtml():
            QMessageBox.warning(self, "تحذير", "يرجى إنشاء الورقة أولاً")
            return
        
        printer = QPrinter(QPrinter.HighResolution)
        printer.setPageSize(QPrinter.A4)
        printer.setOrientation(QPrinter.Landscape)  # أفقي للجدول العريض
        
        dialog = QPrintDialog(printer, self)
        if dialog.exec_() == QPrintDialog.Accepted:
            document = QTextDocument()
            document.setHtml(self.preview_text.toHtml())
            document.print_(printer)
            QMessageBox.information(self, "نجح", "تم إرسال الورقة للطباعة")

def main():
    app = QApplication(sys.argv)
    app.setStyle('Fusion')
    
    window = MonthlyAttendanceSheet()
    window.show()
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
