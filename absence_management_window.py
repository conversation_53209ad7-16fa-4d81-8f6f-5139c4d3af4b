#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
from datetime import datetime, timedelta
from PyQt5.QtWidgets import (QMainWindow, QWidget, QLabel, QPushButton, QVBoxLayout,
                             QHBoxLayout, QGroupBox, QTableWidget, QTableWidgetItem,
                             QComboBox, QDateEdit, QLineEdit, QCheckBox, QSpinBox,
                             QMessageBox, QStatusBar, QAbstractItemView, QHeaderView,
                             QCalendarWidget, QTextEdit, QTabWidget, QSplitter,
                             QProgressBar, QFrame)
from PyQt5.QtCore import Qt, QDate, QTimer
from PyQt5.QtGui import QFont, QColor, QIcon, QPalette

class AbsenceManagementWindow(QMainWindow):
    """نافذة معالجة الغياب مع التقارير"""

    def __init__(self, db_path="data.db", parent=None):
        super().__init__(parent)
        self.db_path = db_path
        self.init_database()
        self.init_ui()
        self.load_data()

    def init_database(self):
        """إنشاء جداول قاعدة البيانات المطلوبة"""
        try:
            import sqlite3
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # إنشاء جدول الغياب
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS absence_records (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    student_id INTEGER,
                    student_name TEXT,
                    absence_date TEXT,
                    notes TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (student_id) REFERENCES جدول_البيانات(id)
                )
            """)

            # إنشاء فهرس للبحث السريع
            cursor.execute("""
                CREATE INDEX IF NOT EXISTS idx_absence_date
                ON absence_records(absence_date)
            """)

            cursor.execute("""
                CREATE INDEX IF NOT EXISTS idx_student_id
                ON absence_records(student_id)
            """)

            # التحقق من وجود بيانات، وإضافة بيانات تجريبية إذا لم تكن موجودة
            cursor.execute("SELECT COUNT(*) FROM جدول_البيانات")
            student_count = cursor.fetchone()[0]

            if student_count == 0:
                print("إضافة بيانات تجريبية...")

                # إضافة بيانات تجريبية للطلاب
                test_students = [
                    ("أحمد محمد علي", "المجموعة الأولى"),
                    ("فاطمة حسن أحمد", "المجموعة الثانية"),
                    ("محمد علي حسن", "المجموعة الأولى"),
                    ("عائشة محمود علي", "المجموعة الثالثة"),
                    ("علي أحمد محمد", "المجموعة الثانية"),
                    ("زينب حسن محمد", "المجموعة الأولى"),
                    ("يوسف علي أحمد", "المجموعة الثالثة"),
                    ("مريم محمد حسن", "المجموعة الثانية")
                ]

                cursor.executemany("""
                    INSERT INTO جدول_البيانات (اسم_الطالب, اسم_المجموعة)
                    VALUES (?, ?)
                """, test_students)

                # إضافة بيانات الأقسام والمجموعات
                test_sections = [
                    ("القسم الأول", "المجموعة الأولى", "أستاذ أحمد محمد"),
                    ("القسم الثاني", "المجموعة الثانية", "أستاذة فاطمة علي"),
                    ("القسم الثالث", "المجموعة الثالثة", "أستاذ محمد حسن")
                ]

                cursor.executemany("""
                    INSERT INTO جدول_المواد_والاقسام (القسم, المجموعة, اسم_الاستاذ)
                    VALUES (?, ?, ?)
                """, test_sections)

                print("تم إضافة البيانات التجريبية")

            conn.commit()
            conn.close()

        except Exception as e:
            print(f"خطأ في إنشاء قاعدة البيانات: {str(e)}")

    def init_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("📊 معالجة الغياب والتقارير")
        self.setGeometry(100, 100, 1400, 900)
        self.setLayoutDirection(Qt.RightToLeft)

        # تطبيق ثيم احترافي
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f8f9fa;
                font-family: 'Calibri', 'Arial', 'Tahoma';
            }
            QGroupBox {
                font-family: 'Calibri', 'Arial', 'Tahoma';
                font-size: 14px;
                font-weight: bold;
                color: #2c3e50;
                border: 2px solid #3498db;
                border-radius: 10px;
                margin-top: 12px;
                padding-top: 12px;
                background-color: white;
                margin-bottom: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                right: 15px;
                padding: 0 8px 0 8px;
                background-color: white;
                font-family: 'Calibri', 'Arial', 'Tahoma';
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton {
                font-family: 'Calibri', 'Arial', 'Tahoma';
                font-size: 13px;
                font-weight: bold;
                padding: 8px 16px;
                border-radius: 6px;
                border: none;
                min-height: 30px;
            }
            QComboBox, QDateEdit, QLineEdit, QSpinBox {
                font-family: 'Calibri', 'Arial', 'Tahoma';
                font-size: 13px;
                padding: 6px;
                border: 2px solid #bdc3c7;
                border-radius: 5px;
                background-color: white;
                min-height: 20px;
            }
            QComboBox:focus, QDateEdit:focus, QLineEdit:focus, QSpinBox:focus {
                border-color: #3498db;
            }
            QTableWidget {
                gridline-color: #bdc3c7;
                background-color: white;
                alternate-background-color: #f8f9fa;
                border: 1px solid #bdc3c7;
                border-radius: 6px;
                font-family: 'Calibri', 'Arial', 'Tahoma';
                font-size: 13px;
            }
            QHeaderView::section {
                background-color: #34495e;
                color: white;
                padding: 10px;
                border: none;
                font-weight: bold;
                font-size: 13px;
                font-family: 'Calibri', 'Arial', 'Tahoma';
            }
            QCheckBox {
                font-family: 'Calibri', 'Arial', 'Tahoma';
                font-size: 13px;
                font-weight: bold;
            }
            QLabel {
                font-family: 'Calibri', 'Arial', 'Tahoma';
            }
            QTabWidget::pane {
                border: 1px solid #bdc3c7;
                background-color: white;
            }
            QTabBar::tab {
                background-color: #ecf0f1;
                color: #2c3e50;
                padding: 8px 16px;
                margin: 2px;
                border-radius: 4px;
                font-family: 'Calibri', 'Arial', 'Tahoma';
                font-size: 13px;
                font-weight: bold;
            }
            QTabBar::tab:selected {
                background-color: #3498db;
                color: white;
            }
        """)

        # العنوان الرئيسي
        self.title_label = QLabel("📊 معالجة الغياب والتقارير", self)
        self.title_label.setAlignment(Qt.AlignCenter)
        self.title_label.setFont(QFont("Calibri", 15, QFont.Bold))
        self.title_label.setStyleSheet("""
            QLabel {
                color: white;
                padding: 18px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #e74c3c, stop:1 #c0392b);
                border-radius: 10px;
            }
        """)
        self.title_label.setGeometry(15, 15, 1370, 60)

        # شريط المعلومات السريعة
        self.create_info_bar()

        # التبويبات الرئيسية
        self.create_main_tabs()

        # شريط الحالة
        self.setStatusBar(QStatusBar())
        self.statusBar().showMessage("جاهز - نظام معالجة الغياب")

    def create_info_bar(self):
        """إنشاء شريط المعلومات السريعة"""
        self.info_widget = QWidget(self)
        self.info_widget.setStyleSheet("""
            QWidget {
                background-color: #ecf0f1;
                border-radius: 8px;
                border: 1px solid #d5dbdb;
            }
        """)
        self.info_widget.setGeometry(15, 85, 1370, 45)

        # معلومات النظام
        self.system_info = QLabel("📊 نظام معالجة الغياب نشط", self.info_widget)
        self.system_info.setFont(QFont("Calibri", 13, QFont.Bold))
        self.system_info.setStyleSheet("color: #27ae60;")
        self.system_info.setGeometry(15, 12, 250, 20)

        # إحصائيات سريعة
        self.today_absences = QLabel("📅 غياب اليوم: جاري التحميل...", self.info_widget)
        self.today_absences.setFont(QFont("Calibri", 13, QFont.Bold))
        self.today_absences.setStyleSheet("color: #e74c3c;")
        self.today_absences.setGeometry(400, 12, 300, 20)

        # إجمالي الغياب
        self.total_absences = QLabel("📈 إجمالي الغياب: جاري التحميل...", self.info_widget)
        self.total_absences.setFont(QFont("Calibri", 13, QFont.Bold))
        self.total_absences.setStyleSheet("color: #f39c12;")
        self.total_absences.setGeometry(800, 12, 350, 20)

        # تحديث المعلومات
        self.update_info_bar()

    def create_main_tabs(self):
        """إنشاء التبويبات الرئيسية"""
        self.tab_widget = QTabWidget(self)
        self.tab_widget.setGeometry(15, 140, 1370, 720)

        # تبويب إدارة الغياب
        self.absence_tab = QWidget()
        self.tab_widget.addTab(self.absence_tab, "📝 إدارة الغياب")
        self.create_absence_management_tab()

        # تبويب التقارير
        self.reports_tab = QWidget()
        self.tab_widget.addTab(self.reports_tab, "📊 التقارير")
        self.create_reports_tab()

        # تبويب الإحصائيات
        self.statistics_tab = QWidget()
        self.tab_widget.addTab(self.statistics_tab, "📈 الإحصائيات")
        self.create_statistics_tab()

    def create_absence_management_tab(self):
        """إنشاء تبويب إدارة الغياب"""
        # منطقة البحث والتصفية
        search_group = QGroupBox("🔍 البحث والتصفية", self.absence_tab)
        search_group.setGeometry(15, 15, 1340, 120)

        # البحث بالاسم
        name_label = QLabel("اسم الطالب:", search_group)
        name_label.setFont(QFont("Calibri", 14, QFont.Bold))
        name_label.setGeometry(20, 40, 80, 25)

        self.student_search = QLineEdit(search_group)
        self.student_search.setPlaceholderText("ابحث عن طالب...")
        self.student_search.setGeometry(110, 40, 200, 25)
        self.student_search.textChanged.connect(self.filter_students)

        # اختيار القسم
        section_label = QLabel("القسم:", search_group)
        section_label.setFont(QFont("Calibri", 14, QFont.Bold))
        section_label.setGeometry(330, 40, 50, 25)

        self.section_combo = QComboBox(search_group)
        self.section_combo.setGeometry(390, 40, 150, 25)
        self.section_combo.currentTextChanged.connect(self.filter_students)

        # اختيار المجموعة
        group_label = QLabel("المجموعة:", search_group)
        group_label.setFont(QFont("Calibri", 14, QFont.Bold))
        group_label.setGeometry(560, 40, 70, 25)

        self.group_combo = QComboBox(search_group)
        self.group_combo.setGeometry(640, 40, 150, 25)
        self.group_combo.currentTextChanged.connect(self.filter_students)

        # اختيار التاريخ
        date_label = QLabel("التاريخ:", search_group)
        date_label.setFont(QFont("Calibri", 14, QFont.Bold))
        date_label.setGeometry(810, 40, 50, 25)

        self.date_edit = QDateEdit(search_group)
        self.date_edit.setDate(QDate.currentDate())
        self.date_edit.setCalendarPopup(True)
        self.date_edit.setGeometry(870, 40, 120, 25)
        self.date_edit.dateChanged.connect(self.load_absence_data)

        # أزرار العمليات
        self.mark_absent_btn = QPushButton("❌ تسجيل غياب", search_group)
        self.mark_absent_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        self.mark_absent_btn.setGeometry(20, 80, 120, 30)
        self.mark_absent_btn.clicked.connect(self.mark_student_absent)

        self.mark_present_btn = QPushButton("✅ تسجيل حضور", search_group)
        self.mark_present_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        self.mark_present_btn.setGeometry(150, 80, 120, 30)
        self.mark_present_btn.clicked.connect(self.mark_student_present)

        self.bulk_absent_btn = QPushButton("📋 غياب جماعي", search_group)
        self.bulk_absent_btn.setStyleSheet("""
            QPushButton {
                background-color: #f39c12;
                color: white;
            }
            QPushButton:hover {
                background-color: #e67e22;
            }
        """)
        self.bulk_absent_btn.setGeometry(280, 80, 120, 30)
        self.bulk_absent_btn.clicked.connect(self.bulk_mark_absent)

        self.refresh_btn = QPushButton("🔄 تحديث البيانات", search_group)
        self.refresh_btn.setStyleSheet("""
            QPushButton {
                background-color: #17a2b8;
                color: white;
            }
            QPushButton:hover {
                background-color: #138496;
            }
        """)
        self.refresh_btn.setGeometry(410, 80, 120, 30)
        self.refresh_btn.clicked.connect(self.refresh_all_data)

        # جدول الطلاب والغياب
        self.students_table = QTableWidget(self.absence_tab)
        self.students_table.setGeometry(15, 145, 1340, 540)
        self.students_table.setColumnCount(8)
        self.students_table.setHorizontalHeaderLabels([
            "اسم الطالب", "القسم", "المجموعة", "الحالة", "عدد أيام الغياب",
            "آخر غياب", "ملاحظات", "تعديل"
        ])

        # تنسيق الجدول
        self.students_table.setAlternatingRowColors(True)
        self.students_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.students_table.setSelectionMode(QAbstractItemView.MultiSelection)
        self.students_table.setSortingEnabled(True)

        # تعيين عرض الأعمدة
        self.students_table.setColumnWidth(0, 200)  # اسم الطالب
        self.students_table.setColumnWidth(1, 120)  # القسم
        self.students_table.setColumnWidth(2, 120)  # المجموعة
        self.students_table.setColumnWidth(3, 100)  # الحالة
        self.students_table.setColumnWidth(4, 120)  # عدد أيام الغياب
        self.students_table.setColumnWidth(5, 120)  # آخر غياب
        self.students_table.setColumnWidth(6, 200)  # ملاحظات
        self.students_table.setColumnWidth(7, 100)  # تعديل

    def create_reports_tab(self):
        """إنشاء تبويب التقارير"""
        # منطقة خيارات التقرير
        report_options_group = QGroupBox("📋 خيارات التقرير", self.reports_tab)
        report_options_group.setGeometry(15, 15, 1340, 120)

        # نوع التقرير
        report_type_label = QLabel("نوع التقرير:", report_options_group)
        report_type_label.setFont(QFont("Calibri", 14, QFont.Bold))
        report_type_label.setGeometry(20, 40, 80, 25)

        self.report_type_combo = QComboBox(report_options_group)
        self.report_type_combo.addItems([
            "تقرير الغياب اليومي",
            "تقرير الغياب الأسبوعي",
            "تقرير الغياب الشهري",
            "تقرير الطلاب الأكثر غياباً",
            "تقرير الغياب حسب القسم",
            "تقرير الغياب حسب المجموعة",
            "تقرير مقارنة الأقسام"
        ])
        self.report_type_combo.setGeometry(110, 40, 200, 25)

        # فترة التقرير
        period_label = QLabel("الفترة:", report_options_group)
        period_label.setFont(QFont("Calibri", 14, QFont.Bold))
        period_label.setGeometry(330, 40, 50, 25)

        self.start_date = QDateEdit(report_options_group)
        self.start_date.setDate(QDate.currentDate().addDays(-30))
        self.start_date.setCalendarPopup(True)
        self.start_date.setGeometry(390, 40, 120, 25)

        to_label = QLabel("إلى", report_options_group)
        to_label.setFont(QFont("Calibri", 14, QFont.Bold))
        to_label.setGeometry(520, 40, 20, 25)

        self.end_date = QDateEdit(report_options_group)
        self.end_date.setDate(QDate.currentDate())
        self.end_date.setCalendarPopup(True)
        self.end_date.setGeometry(550, 40, 120, 25)

        # تصفية حسب القسم
        filter_section_label = QLabel("القسم:", report_options_group)
        filter_section_label.setFont(QFont("Calibri", 14, QFont.Bold))
        filter_section_label.setGeometry(690, 40, 50, 25)

        self.report_section_combo = QComboBox(report_options_group)
        self.report_section_combo.addItem("جميع الأقسام")
        self.report_section_combo.setGeometry(750, 40, 150, 25)

        # أزرار التقارير
        self.generate_report_btn = QPushButton("📊 إنشاء التقرير", report_options_group)
        self.generate_report_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        self.generate_report_btn.setGeometry(20, 80, 120, 30)
        self.generate_report_btn.clicked.connect(self.generate_report)

        self.export_report_btn = QPushButton("📤 تصدير التقرير", report_options_group)
        self.export_report_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        self.export_report_btn.setGeometry(150, 80, 120, 30)
        self.export_report_btn.clicked.connect(self.export_report)

        self.print_report_btn = QPushButton("🖨️ طباعة التقرير", report_options_group)
        self.print_report_btn.setStyleSheet("""
            QPushButton {
                background-color: #9b59b6;
                color: white;
            }
            QPushButton:hover {
                background-color: #8e44ad;
            }
        """)
        self.print_report_btn.setGeometry(280, 80, 120, 30)
        self.print_report_btn.clicked.connect(self.print_report)

        # منطقة عرض التقرير
        self.report_table = QTableWidget(self.reports_tab)
        self.report_table.setGeometry(15, 145, 1340, 540)
        self.report_table.setAlternatingRowColors(True)
        self.report_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.report_table.setSortingEnabled(True)

    def create_statistics_tab(self):
        """إنشاء تبويب الإحصائيات"""
        # إحصائيات عامة
        general_stats_group = QGroupBox("📈 إحصائيات عامة", self.statistics_tab)
        general_stats_group.setGeometry(15, 15, 650, 200)

        # إجمالي الطلاب
        self.total_students_label = QLabel("👥 إجمالي الطلاب: جاري التحميل...", general_stats_group)
        self.total_students_label.setFont(QFont("Calibri", 13, QFont.Bold))
        self.total_students_label.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                padding: 8px;
                background-color: #ecf0f1;
                border-radius: 5px;
                border: 1px solid #bdc3c7;
            }
        """)
        self.total_students_label.setGeometry(20, 40, 600, 30)

        # الطلاب الحاضرين اليوم
        self.present_today_label = QLabel("✅ الحاضرين اليوم: جاري التحميل...", general_stats_group)
        self.present_today_label.setFont(QFont("Calibri", 13, QFont.Bold))
        self.present_today_label.setStyleSheet("""
            QLabel {
                color: #27ae60;
                padding: 8px;
                background-color: #d5f4e6;
                border-radius: 5px;
                border: 1px solid #27ae60;
            }
        """)
        self.present_today_label.setGeometry(20, 80, 600, 30)

        # الطلاب الغائبين اليوم
        self.absent_today_label = QLabel("❌ الغائبين اليوم: جاري التحميل...", general_stats_group)
        self.absent_today_label.setFont(QFont("Calibri", 13, QFont.Bold))
        self.absent_today_label.setStyleSheet("""
            QLabel {
                color: #e74c3c;
                padding: 8px;
                background-color: #fadbd8;
                border-radius: 5px;
                border: 1px solid #e74c3c;
            }
        """)
        self.absent_today_label.setGeometry(20, 120, 600, 30)

        # نسبة الحضور
        self.attendance_rate_label = QLabel("📊 نسبة الحضور: جاري التحميل...", general_stats_group)
        self.attendance_rate_label.setFont(QFont("Calibri", 13, QFont.Bold))
        self.attendance_rate_label.setStyleSheet("""
            QLabel {
                color: #3498db;
                padding: 8px;
                background-color: #d6eaf8;
                border-radius: 5px;
                border: 1px solid #3498db;
            }
        """)
        self.attendance_rate_label.setGeometry(20, 160, 600, 30)

        # إحصائيات الأقسام
        sections_stats_group = QGroupBox("🏫 إحصائيات الأقسام", self.statistics_tab)
        sections_stats_group.setGeometry(675, 15, 680, 200)

        self.sections_stats_table = QTableWidget(sections_stats_group)
        self.sections_stats_table.setGeometry(20, 40, 640, 140)
        self.sections_stats_table.setColumnCount(4)
        self.sections_stats_table.setHorizontalHeaderLabels([
            "القسم", "إجمالي الطلاب", "الحاضرين", "نسبة الحضور"
        ])
        self.sections_stats_table.setAlternatingRowColors(True)

        # جدول أكثر الطلاب غياباً
        most_absent_group = QGroupBox("⚠️ الطلاب الأكثر غياباً", self.statistics_tab)
        most_absent_group.setGeometry(15, 225, 1340, 460)

        self.most_absent_table = QTableWidget(most_absent_group)
        self.most_absent_table.setGeometry(20, 40, 1300, 400)
        self.most_absent_table.setColumnCount(6)
        self.most_absent_table.setHorizontalHeaderLabels([
            "اسم الطالب", "القسم", "المجموعة", "عدد أيام الغياب",
            "نسبة الغياب", "آخر حضور"
        ])
        self.most_absent_table.setAlternatingRowColors(True)
        self.most_absent_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.most_absent_table.setSortingEnabled(True)

    def load_data(self):
        """تحميل البيانات الأساسية"""
        self.load_sections_and_groups()
        self.load_students_data()
        self.update_statistics()

    def load_sections_and_groups(self):
        """تحميل الأقسام والمجموعات من الجداول المتاحة"""
        try:
            import sqlite3
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # التحقق من الجداول الموجودة
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            available_tables = [table[0] for table in cursor.fetchall()]

            # البحث عن جدول الأقسام المناسب
            sections_table = None
            sections_column = None
            groups_column = None

            # قائمة الجداول المحتملة للأقسام
            possible_tables = [
                'جدول_المواد_والاقسام',
                'جدول_البيانات',
                'sections',
                'الأقسام'
            ]

            for table_name in possible_tables:
                if table_name in available_tables:
                    try:
                        cursor.execute(f"PRAGMA table_info(`{table_name}`)")
                        columns = [col[1] for col in cursor.fetchall()]

                        # البحث عن أعمدة الأقسام والمجموعات
                        for col in columns:
                            if any(keyword in col.lower() for keyword in ['قسم', 'section']):
                                sections_column = col
                                sections_table = table_name
                            if any(keyword in col.lower() for keyword in ['مجموعة', 'group']):
                                groups_column = col

                        if sections_column:
                            break

                    except Exception:
                        continue

            # إذا لم نجد جدول مناسب، استخدم الافتراضي
            if not sections_table:
                sections_table = 'جدول_المواد_والاقسام'
                sections_column = 'القسم'
                groups_column = 'المجموعة'

            # تحميل الأقسام
            self.section_combo.clear()
            self.section_combo.addItem("جميع الأقسام")
            self.report_section_combo.clear()
            self.report_section_combo.addItem("جميع الأقسام")

            try:
                query = f"SELECT DISTINCT `{sections_column}` FROM `{sections_table}` WHERE `{sections_column}` IS NOT NULL AND `{sections_column}` != ''"
                cursor.execute(query)
                sections = cursor.fetchall()

                for section in sections:
                    if section[0]:
                        self.section_combo.addItem(section[0])
                        self.report_section_combo.addItem(section[0])
            except Exception as e:
                print(f"خطأ في تحميل الأقسام: {e}")

            # تحميل المجموعات
            self.group_combo.clear()
            self.group_combo.addItem("جميع المجموعات")

            if groups_column:
                try:
                    query = f"SELECT DISTINCT `{groups_column}` FROM `{sections_table}` WHERE `{groups_column}` IS NOT NULL AND `{groups_column}` != ''"
                    cursor.execute(query)
                    groups = cursor.fetchall()

                    for group in groups:
                        if group[0]:
                            self.group_combo.addItem(group[0])
                except Exception as e:
                    print(f"خطأ في تحميل المجموعات: {e}")

            conn.close()

        except Exception as e:
            self.statusBar().showMessage(f"خطأ في تحميل الأقسام والمجموعات: {str(e)}")

    def load_students_data(self):
        """تحميل بيانات الطلاب من الجداول المتاحة"""
        try:
            import sqlite3
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # التحقق من الجداول الموجودة
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            available_tables = [table[0] for table in cursor.fetchall()]

            # البحث عن جدول الطلاب المناسب
            students_table = None
            students_name_column = None
            students_group_column = None

            # فحص جدول البيانات مباشرة
            if 'جدول_البيانات' in available_tables:
                try:
                    # فحص وجود الأعمدة
                    cursor.execute(f"PRAGMA table_info(جدول_البيانات)")
                    columns = [col[1] for col in cursor.fetchall()]

                    print(f"🔍 الأعمدة الموجودة في جدول_البيانات: {columns}")

                    # تحديد الأعمدة الصحيحة بناءً على الأسماء الفعلية
                    name_column = None
                    group_column = None

                    # البحث عن عمود اسم الطالب/التلميذ
                    if 'اسم_التلميذ' in columns:
                        name_column = 'اسم_التلميذ'
                    elif 'اسم_الطالب' in columns:
                        name_column = 'اسم_الطالب'
                    elif 'student_name' in columns:
                        name_column = 'student_name'
                    elif 'الاسم' in columns:
                        name_column = 'الاسم'
                    else:
                        # البحث في الأعمدة الموجودة
                        for col in columns:
                            if 'اسم' in col and ('طالب' in col or 'تلميذ' in col):
                                name_column = col
                                break

                    # البحث عن عمود المجموعة
                    if 'اسم_المجموعة' in columns:
                        group_column = 'اسم_المجموعة'
                    elif 'المجموعة' in columns:
                        group_column = 'المجموعة'
                    elif 'group_name' in columns:
                        group_column = 'group_name'
                    else:
                        # البحث في الأعمدة الموجودة
                        for col in columns:
                            if 'مجموعة' in col:
                                group_column = col
                                break

                    if name_column:
                        students_table = 'جدول_البيانات'
                        students_name_column = name_column
                        students_group_column = group_column or name_column
                        print(f"✅ تم العثور على الأعمدة: اسم={name_column}, مجموعة={group_column}")
                    else:
                        print("❌ لم يتم العثور على عمود اسم الطالب")

                except Exception as e:
                    print(f"❌ خطأ في فحص جدول البيانات: {e}")

            if not students_table:
                # إنشاء جدول تجريبي إذا لم يوجد
                self.create_sample_data()
                students_table = 'جدول_البيانات'
                students_name_column = 'اسم_التلميذ'
                students_group_column = 'اسم_المجموعة'

            # طباعة معلومات التشخيص
            print(f"🔍 جدول الطلاب المستخدم: {students_table}")
            print(f"📝 عمود الاسم: {students_name_column}")
            print(f"👥 عمود المجموعة: {students_group_column}")

            current_date = self.date_edit.date().toString("yyyy-MM-dd") if hasattr(self, 'date_edit') else None

            # جلب جميع الطلاب
            query = f"""
                SELECT
                    COALESCE(id, rowid) as id,
                    `{students_name_column}` as student_name,
                    `{students_group_column}` as group_name
                FROM `{students_table}`
                WHERE `{students_name_column}` IS NOT NULL AND `{students_name_column}` != ''
                ORDER BY `{students_name_column}`
            """

            print(f"🔍 الاستعلام المستخدم: {query}")
            cursor.execute(query)
            students_basic = cursor.fetchall()
            print(f"📊 عدد الطلاب المجلبين: {len(students_basic)}")

            if len(students_basic) == 0:
                print("⚠️ لم يتم العثور على طلاب!")
                print("🔧 محاولة استعلام بديل...")

                # استعلام بديل مبسط
                try:
                    cursor.execute(f"SELECT * FROM `{students_table}` LIMIT 5")
                    all_data = cursor.fetchall()
                    print(f"📋 عينة من جميع البيانات: {all_data}")
                except Exception as e:
                    print(f"❌ خطأ في الاستعلام البديل: {e}")
            students = []

            for student_basic in students_basic:
                student_id, student_name, group_name = student_basic

                # جلب معلومات القسم
                cursor.execute("""
                    SELECT القسم, المجموعة
                    FROM جدول_المواد_والاقسام
                    WHERE المجموعة = ?
                    LIMIT 1
                """, (group_name,))

                section_info = cursor.fetchone()
                section = section_info[0] if section_info else group_name or "غير محدد"
                group = section_info[1] if section_info else group_name or "غير محدد"

                # فحص الغياب لليوم المحدد
                if current_date:
                    cursor.execute("""
                        SELECT id, notes
                        FROM absence_records
                        WHERE student_id = ? AND DATE(absence_date) = ?
                    """, (student_id, current_date))
                    absence_today = cursor.fetchone()
                else:
                    absence_today = None

                # حساب إجمالي الغياب
                cursor.execute("""
                    SELECT COUNT(*), MAX(absence_date)
                    FROM absence_records
                    WHERE student_id = ?
                """, (student_id,))

                absence_stats = cursor.fetchone()
                total_absences = absence_stats[0] if absence_stats else 0
                last_absence = absence_stats[1] if absence_stats and absence_stats[1] else "لا يوجد"

                # تحديد الحالة
                status = "غائب" if absence_today else "حاضر"
                notes = absence_today[1] if absence_today else ""

                students.append((
                    student_name,
                    section,
                    group,
                    status,
                    total_absences,
                    last_absence,
                    notes
                ))

            self.students_table.setRowCount(len(students))

            for row, student in enumerate(students):
                # اسم الطالب
                name_item = QTableWidgetItem(str(student[0] or ""))
                name_item.setFont(QFont("Calibri", 13, QFont.Bold))
                self.students_table.setItem(row, 0, name_item)

                # القسم
                section_item = QTableWidgetItem(str(student[1] or ""))
                self.students_table.setItem(row, 1, section_item)

                # المجموعة
                group_item = QTableWidgetItem(str(student[2] or ""))
                self.students_table.setItem(row, 2, group_item)

                # الحالة
                status_item = QTableWidgetItem(str(student[3]))
                if student[3] == "حاضر":
                    status_item.setStyleSheet("background-color: #d5f4e6; color: #27ae60;")
                else:
                    status_item.setStyleSheet("background-color: #fadbd8; color: #e74c3c;")
                status_item.setFont(QFont("Calibri", 13, QFont.Bold))
                self.students_table.setItem(row, 3, status_item)

                # عدد أيام الغياب
                absence_count = QTableWidgetItem(str(student[4]))
                absence_count.setTextAlignment(Qt.AlignCenter)
                if student[4] > 10:
                    absence_count.setStyleSheet("background-color: #fadbd8; color: #e74c3c;")
                elif student[4] > 5:
                    absence_count.setStyleSheet("background-color: #fff3cd; color: #856404;")
                self.students_table.setItem(row, 4, absence_count)

                # آخر غياب
                last_absence = QTableWidgetItem(str(student[5] or "لا يوجد"))
                self.students_table.setItem(row, 5, last_absence)

                # ملاحظات
                notes_item = QTableWidgetItem(str(student[6] or ""))
                self.students_table.setItem(row, 6, notes_item)

                # زر تعديل
                edit_btn = QPushButton("✏️ تعديل")
                edit_btn.setStyleSheet("""
                    QPushButton {
                        background-color: #17a2b8;
                        color: white;
                        border-radius: 4px;
                        padding: 4px 8px;
                    }
                    QPushButton:hover {
                        background-color: #138496;
                    }
                """)
                edit_btn.clicked.connect(lambda _, r=row: self.edit_student_absence(r))
                self.students_table.setCellWidget(row, 7, edit_btn)

            conn.close()
            self.statusBar().showMessage(f"تم تحميل {len(students)} طالب")

        except Exception as e:
            self.statusBar().showMessage(f"خطأ في تحميل بيانات الطلاب: {str(e)}")

    def filter_students(self):
        """تصفية الطلاب حسب المعايير المحددة"""
        search_text = self.student_search.text().lower()
        selected_section = self.section_combo.currentText()
        selected_group = self.group_combo.currentText()

        for row in range(self.students_table.rowCount()):
            show_row = True

            # تصفية بالاسم
            if search_text:
                name_item = self.students_table.item(row, 0)
                if name_item and search_text not in name_item.text().lower():
                    show_row = False

            # تصفية بالقسم
            if selected_section != "جميع الأقسام":
                section_item = self.students_table.item(row, 1)
                if not section_item or section_item.text() != selected_section:
                    show_row = False

            # تصفية بالمجموعة
            if selected_group != "جميع المجموعات":
                group_item = self.students_table.item(row, 2)
                if not group_item or group_item.text() != selected_group:
                    show_row = False

            self.students_table.setRowHidden(row, not show_row)

    def update_info_bar(self):
        """تحديث شريط المعلومات"""
        try:
            import sqlite3
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # غياب اليوم
            cursor.execute("""
                SELECT COUNT(*) FROM absence_records
                WHERE DATE(absence_date) = DATE('now')
            """)
            today_absences = cursor.fetchone()[0]
            self.today_absences.setText(f"📅 غياب اليوم: {today_absences} طالب")

            # إجمالي الغياب
            cursor.execute("SELECT COUNT(*) FROM absence_records")
            total_absences = cursor.fetchone()[0]
            self.total_absences.setText(f"📈 إجمالي الغياب: {total_absences} حالة")

            conn.close()

        except Exception as e:
            self.statusBar().showMessage(f"خطأ في تحديث المعلومات: {str(e)}")

    def update_statistics(self):
        """تحديث الإحصائيات"""
        try:
            import sqlite3
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # إجمالي الطلاب
            cursor.execute("SELECT COUNT(*) FROM جدول_البيانات")
            total_students = cursor.fetchone()[0]

            # الغائبين اليوم
            cursor.execute("""
                SELECT COUNT(*) FROM absence_records
                WHERE DATE(absence_date) = DATE('now')
            """)
            absent_today = cursor.fetchone()[0]

            # الحاضرين اليوم
            present_today = total_students - absent_today

            # نسبة الحضور
            attendance_rate = (present_today / total_students * 100) if total_students > 0 else 0

            # تحديث التسميات
            self.total_students_label.setText(f"👥 إجمالي الطلاب: {total_students} طالب")
            self.present_today_label.setText(f"✅ الحاضرين اليوم: {present_today} طالب")
            self.absent_today_label.setText(f"❌ الغائبين اليوم: {absent_today} طالب")
            self.attendance_rate_label.setText(f"📊 نسبة الحضور: {attendance_rate:.1f}%")

            conn.close()

        except Exception as e:
            self.statusBar().showMessage(f"خطأ في تحديث الإحصائيات: {str(e)}")

    def mark_student_absent(self):
        """تسجيل غياب الطالب المحدد"""
        selected_rows = self.students_table.selectionModel().selectedRows()
        if not selected_rows:
            from PyQt5.QtWidgets import QMessageBox
            QMessageBox.warning(self, "تحذير", "يرجى اختيار طالب لتسجيل غيابه")
            return

        try:
            import sqlite3
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # إنشاء جدول الغياب إذا لم يكن موجوداً
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS absence_records (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    student_id INTEGER,
                    student_name TEXT,
                    absence_date TEXT,
                    notes TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)

            success_count = 0
            for index in selected_rows:
                row = index.row()
                student_name = self.students_table.item(row, 0).text()

                # البحث عن معرف الطالب
                cursor.execute("SELECT id FROM جدول_البيانات WHERE اسم_الطالب = ?", (student_name,))
                student_result = cursor.fetchone()

                if student_result:
                    student_id = student_result[0]
                    current_date = self.date_edit.date().toString("yyyy-MM-dd")

                    # التحقق من عدم وجود تسجيل غياب لنفس اليوم
                    cursor.execute("""
                        SELECT id FROM absence_records
                        WHERE student_id = ? AND DATE(absence_date) = ?
                    """, (student_id, current_date))

                    if not cursor.fetchone():
                        cursor.execute("""
                            INSERT INTO absence_records (student_id, student_name, absence_date, notes)
                            VALUES (?, ?, ?, ?)
                        """, (student_id, student_name, current_date, "غياب مسجل"))
                        success_count += 1

            conn.commit()
            conn.close()

            if success_count > 0:
                from PyQt5.QtWidgets import QMessageBox
                QMessageBox.information(self, "نجح", f"تم تسجيل غياب {success_count} طالب")
                self.load_students_data()
                self.update_info_bar()
                self.update_statistics()

        except Exception as e:
            from PyQt5.QtWidgets import QMessageBox
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في تسجيل الغياب:\n{str(e)}")

    def mark_student_present(self):
        """تسجيل حضور الطالب المحدد (إلغاء الغياب)"""
        selected_rows = self.students_table.selectionModel().selectedRows()
        if not selected_rows:
            from PyQt5.QtWidgets import QMessageBox
            QMessageBox.warning(self, "تحذير", "يرجى اختيار طالب لتسجيل حضوره")
            return

        try:
            import sqlite3
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            success_count = 0
            for index in selected_rows:
                row = index.row()
                student_name = self.students_table.item(row, 0).text()

                # البحث عن معرف الطالب
                cursor.execute("SELECT id FROM جدول_البيانات WHERE اسم_الطالب = ?", (student_name,))
                student_result = cursor.fetchone()

                if student_result:
                    student_id = student_result[0]
                    current_date = self.date_edit.date().toString("yyyy-MM-dd")

                    # حذف تسجيل الغياب لنفس اليوم
                    cursor.execute("""
                        DELETE FROM absence_records
                        WHERE student_id = ? AND DATE(absence_date) = ?
                    """, (student_id, current_date))

                    if cursor.rowcount > 0:
                        success_count += 1

            conn.commit()
            conn.close()

            if success_count > 0:
                from PyQt5.QtWidgets import QMessageBox
                QMessageBox.information(self, "نجح", f"تم تسجيل حضور {success_count} طالب")
                self.load_students_data()
                self.update_info_bar()
                self.update_statistics()

        except Exception as e:
            from PyQt5.QtWidgets import QMessageBox
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في تسجيل الحضور:\n{str(e)}")

    def bulk_mark_absent(self):
        """تسجيل غياب جماعي"""
        from PyQt5.QtWidgets import QMessageBox
        reply = QMessageBox.question(
            self, "تأكيد الغياب الجماعي",
            "هل أنت متأكد من تسجيل غياب جميع الطلاب المحددين؟",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            self.mark_student_absent()

    def edit_student_absence(self, row):
        """تعديل بيانات غياب الطالب"""
        student_name = self.students_table.item(row, 0).text()
        from PyQt5.QtWidgets import QMessageBox
        QMessageBox.information(self, "تعديل الغياب", f"تعديل بيانات غياب الطالب: {student_name}")
        # يمكن إضافة نافذة تعديل مفصلة هنا

    def refresh_all_data(self):
        """تحديث جميع البيانات"""
        try:
            self.load_sections_and_groups()
            self.load_students_data()
            self.update_info_bar()
            self.update_statistics()
            self.statusBar().showMessage("تم تحديث جميع البيانات")
        except Exception as e:
            from PyQt5.QtWidgets import QMessageBox
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في تحديث البيانات:\n{str(e)}")

    def create_sample_data(self):
        """إنشاء بيانات تجريبية للاختبار"""
        try:
            import sqlite3
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # إنشاء جدول البيانات
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS جدول_البيانات (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    اسم_التلميذ TEXT NOT NULL,
                    اسم_المجموعة TEXT,
                    القسم TEXT,
                    رقم_الهاتف TEXT,
                    العنوان TEXT
                )
            """)

            # إنشاء جدول المواد والأقسام
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS جدول_المواد_والاقسام (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    القسم TEXT NOT NULL,
                    المجموعة TEXT,
                    اسم_الاستاذ TEXT,
                    المادة TEXT
                )
            """)

            # التحقق من وجود بيانات
            cursor.execute("SELECT COUNT(*) FROM جدول_البيانات")
            student_count = cursor.fetchone()[0]

            if student_count == 0:
                # إضافة طلاب تجريبيين
                sample_students = [
                    ("أحمد محمد علي", "المجموعة الأولى", "القسم الأول"),
                    ("فاطمة حسن أحمد", "المجموعة الثانية", "القسم الثاني"),
                    ("محمد علي حسن", "المجموعة الأولى", "القسم الأول"),
                    ("عائشة محمود علي", "المجموعة الثالثة", "القسم الثالث"),
                    ("علي أحمد محمد", "المجموعة الثانية", "القسم الثاني"),
                    ("زينب حسن محمد", "المجموعة الأولى", "القسم الأول"),
                    ("يوسف علي أحمد", "المجموعة الثالثة", "القسم الثالث"),
                    ("مريم محمد حسن", "المجموعة الثانية", "القسم الثاني"),
                    ("خالد حسام الدين", "المجموعة الأولى", "القسم الأول"),
                    ("نور الهدى محمد", "المجموعة الثالثة", "القسم الثالث")
                ]

                cursor.executemany("""
                    INSERT INTO جدول_البيانات (اسم_الطالب, اسم_المجموعة, القسم)
                    VALUES (?, ?, ?)
                """, sample_students)

                # إضافة أقسام ومجموعات
                sample_sections = [
                    ("القسم الأول", "المجموعة الأولى", "أستاذ أحمد محمد", "الرياضيات"),
                    ("القسم الثاني", "المجموعة الثانية", "أستاذة فاطمة علي", "العلوم"),
                    ("القسم الثالث", "المجموعة الثالثة", "أستاذ محمد حسن", "اللغة العربية")
                ]

                cursor.executemany("""
                    INSERT INTO جدول_المواد_والاقسام (القسم, المجموعة, اسم_الاستاذ, المادة)
                    VALUES (?, ?, ?, ?)
                """, sample_sections)

                conn.commit()
                print("تم إنشاء بيانات تجريبية للطلاب والأقسام")

            conn.close()

        except Exception as e:
            print(f"خطأ في إنشاء البيانات التجريبية: {e}")

    def load_absence_data(self):
        """تحميل بيانات الغياب للتاريخ المحدد"""
        self.load_students_data()
        self.update_info_bar()
        self.update_statistics()

    def generate_report(self):
        """إنشاء التقرير المحدد"""
        report_type = self.report_type_combo.currentText()
        start_date = self.start_date.date().toString("yyyy-MM-dd")
        end_date = self.end_date.date().toString("yyyy-MM-dd")

        try:
            import sqlite3
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            if report_type == "تقرير الغياب اليومي":
                self.generate_daily_report(cursor, end_date)
            elif report_type == "تقرير الغياب الشهري":
                self.generate_monthly_report(cursor, start_date, end_date)
            elif report_type == "تقرير الطلاب الأكثر غياباً":
                self.generate_most_absent_report(cursor, start_date, end_date)

            conn.close()
            self.statusBar().showMessage(f"تم إنشاء {report_type}")

        except Exception as e:
            from PyQt5.QtWidgets import QMessageBox
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في إنشاء التقرير:\n{str(e)}")

    def generate_daily_report(self, cursor, date):
        """إنشاء تقرير الغياب اليومي"""
        cursor.execute("""
            SELECT
                ar.student_name,
                m.القسم,
                m.المجموعة,
                ar.absence_date,
                ar.notes
            FROM absence_records ar
            LEFT JOIN جدول_البيانات b ON ar.student_id = b.id
            LEFT JOIN جدول_المواد_والاقسام m ON b.اسم_المجموعة = m.المجموعة
            WHERE DATE(ar.absence_date) = ?
            ORDER BY ar.student_name
        """, (date,))

        results = cursor.fetchall()

        self.report_table.setColumnCount(5)
        self.report_table.setHorizontalHeaderLabels([
            "اسم الطالب", "القسم", "المجموعة", "تاريخ الغياب", "ملاحظات"
        ])
        self.report_table.setRowCount(len(results))

        for row, data in enumerate(results):
            for col, value in enumerate(data):
                item = QTableWidgetItem(str(value or ""))
                self.report_table.setItem(row, col, item)

    def generate_monthly_report(self, cursor, start_date, end_date):
        """إنشاء تقرير الغياب الشهري"""
        cursor.execute("""
            SELECT
                ar.student_name,
                m.القسم,
                COUNT(*) as absence_count,
                GROUP_CONCAT(DATE(ar.absence_date)) as absence_dates
            FROM absence_records ar
            LEFT JOIN جدول_البيانات b ON ar.student_id = b.id
            LEFT JOIN جدول_المواد_والاقسام m ON b.اسم_المجموعة = m.المجموعة
            WHERE DATE(ar.absence_date) BETWEEN ? AND ?
            GROUP BY ar.student_id, ar.student_name, m.القسم
            ORDER BY absence_count DESC
        """, (start_date, end_date))

        results = cursor.fetchall()

        self.report_table.setColumnCount(4)
        self.report_table.setHorizontalHeaderLabels([
            "اسم الطالب", "القسم", "عدد أيام الغياب", "تواريخ الغياب"
        ])
        self.report_table.setRowCount(len(results))

        for row, data in enumerate(results):
            for col, value in enumerate(data):
                item = QTableWidgetItem(str(value or ""))
                if col == 2 and int(value) > 10:  # عدد أيام الغياب
                    item.setStyleSheet("background-color: #fadbd8; color: #e74c3c;")
                self.report_table.setItem(row, col, item)

    def generate_most_absent_report(self, cursor, start_date, end_date):
        """إنشاء تقرير الطلاب الأكثر غياباً"""
        cursor.execute("""
            SELECT
                ar.student_name,
                m.القسم,
                m.المجموعة,
                COUNT(*) as absence_count,
                ROUND(COUNT(*) * 100.0 / 30, 2) as absence_percentage,
                MAX(ar.absence_date) as last_absence
            FROM absence_records ar
            LEFT JOIN جدول_البيانات b ON ar.student_id = b.id
            LEFT JOIN جدول_المواد_والاقسام m ON b.اسم_المجموعة = m.المجموعة
            WHERE DATE(ar.absence_date) BETWEEN ? AND ?
            GROUP BY ar.student_id, ar.student_name, m.القسم, m.المجموعة
            HAVING absence_count >= 3
            ORDER BY absence_count DESC
            LIMIT 50
        """, (start_date, end_date))

        results = cursor.fetchall()

        self.report_table.setColumnCount(6)
        self.report_table.setHorizontalHeaderLabels([
            "اسم الطالب", "القسم", "المجموعة", "عدد أيام الغياب",
            "نسبة الغياب", "آخر غياب"
        ])
        self.report_table.setRowCount(len(results))

        for row, data in enumerate(results):
            for col, value in enumerate(data):
                item = QTableWidgetItem(str(value or ""))
                if col == 3 and int(value) > 10:  # عدد أيام الغياب
                    item.setStyleSheet("background-color: #fadbd8; color: #e74c3c;")
                elif col == 4 and float(value) > 30:  # نسبة الغياب
                    item.setStyleSheet("background-color: #fadbd8; color: #e74c3c;")
                self.report_table.setItem(row, col, item)

    def export_report(self):
        """تصدير التقرير"""
        from PyQt5.QtWidgets import QMessageBox
        QMessageBox.information(self, "تصدير التقرير", "سيتم إضافة وظيفة التصدير قريباً")

    def print_report(self):
        """طباعة التقرير"""
        from PyQt5.QtWidgets import QMessageBox
        QMessageBox.information(self, "طباعة التقرير", "سيتم إضافة وظيفة الطباعة قريباً")

# وظيفة اختبار النافذة
def main():
    """تشغيل النافذة للاختبار"""
    import sys
    from PyQt5.QtWidgets import QApplication

    app = QApplication(sys.argv)
    app.setStyle('Fusion')

    window = AbsenceManagementWindow()
    window.show()

    sys.exit(app.exec_())

if __name__ == "__main__":
    main()