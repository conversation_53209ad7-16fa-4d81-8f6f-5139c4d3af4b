#!/usr/bin/env python
# -*- coding: utf-8 -*-

import sys
import os
import sqlite3
from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QLabel, QPushButton, QTableWidget, QTableWidgetItem, QFrame, 
    QMessageBox, QHeaderView, QAbstractItemView, QComboBox
)
from PyQt5.QtGui import QFont, QIcon, QColor
from PyQt5.QtCore import Qt
from datetime import datetime
import json

class DataViewWindow(QMainWindow):
    """نافذة عرض البيانات مع جدول احترافي"""
    
    def __init__(self, parent=None, db_path="data.db"):
        super().__init__(parent)
        self.db_path = db_path
        self.setupUI()
        self.load_filter_options()  # تحميل خيارات التصفية أولاً فقط
        # لا نحمل البيانات عند بدء التشغيل

    def setupUI(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("عرض بيانات الطلاب")
        # إزالة showMaximized من هنا
        self.setLayoutDirection(Qt.RightToLeft)
        
        # تطبيق نمط احترافي للنافذة الرئيسية
        self.setStyleSheet("""
            QMainWindow {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 1, y2: 1,
                    stop: 0 #f8f9fc,
                    stop: 1 #e9ecef
                );
            }        """)
        
        # إنشاء الواجهة المركزية
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(5, 5, 5, 5)
        main_layout.setSpacing(2)
        
        # إطار الأزرار
        buttons_frame = QFrame()
        buttons_frame.setMaximumHeight(80)
        buttons_frame.setFixedHeight(80)
        buttons_frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 2px solid #bdc3c7;
                border-radius: 10px;
                padding: 1px;
                margin: 1px 5px;
            }
        """)
        
        buttons_layout = QHBoxLayout(buttons_frame)
        buttons_layout.setSpacing(5)
        
        # إنشاء الأزرار المطلوبة
        btn1 = self.create_action_button("📝 التسجيل وإعادة التسجيل", "#27ae60")
        btn2 = self.create_action_button("💰 أداء الواجبات الشهرية", "#e67e22")
        btn3 = self.create_action_button("🖨️ طباعة تقرير مفصل", "#6f42c1")
        btn4 = self.create_action_button("📊 تقرير القسم الشهري", "#17a2b8")
        btn5 = self.create_action_button("📈 تقرير القسم السنوي", "#fd7e14")
        btn6 = self.create_action_button("🔄 التعديل الجماعي", "#8e44ad")
        btn7 = self.create_action_button("📋 أداء الواجبات الشهرية لمجموعة", "#ff6b35")

        # ربط الأزرار بالوظائف
        btn1.clicked.connect(self.handle_registration)
        btn2.clicked.connect(self.handle_monthly_duties)
        btn3.clicked.connect(self.handle_detailed_report)
        btn4.clicked.connect(self.handle_section_monthly_report)
        btn5.clicked.connect(self.handle_section_yearly_report)
        btn6.clicked.connect(self.handle_bulk_edit)
        btn7.clicked.connect(self.handle_group_monthly_duties)
        
        # إضافة الأزرار للتخطيط
        buttons_layout.addWidget(btn1)
        buttons_layout.addWidget(btn2)
        buttons_layout.addWidget(btn3)
        buttons_layout.addWidget(btn4)
        buttons_layout.addWidget(btn5)
        buttons_layout.addWidget(btn6)
        buttons_layout.addWidget(btn7)
        buttons_layout.addStretch()
        
        main_layout.addWidget(buttons_frame)
        
        # إطار التصفية
        filter_frame = QFrame()
        filter_frame.setMaximumHeight(80)
        filter_frame.setStyleSheet("""
            QFrame {
                background-color: #f8f9fa;
                border: 2px solid #dee2e6;
                border-radius: 8px;
                padding: 5px;
                margin: 5px 0px;
            }
        """)
        
        filter_layout = QHBoxLayout(filter_frame)
        filter_layout.setSpacing(5)
        
        # تسمية التصفية
        filter_label = QLabel("🔍 تصفية البيانات:")
        filter_label.setFont(QFont("Calibri", 13, QFont.Bold))
        filter_label.setStyleSheet("color: #495057; border: none;")
        
        # قائمة تصفية الأقسام
        section_filter_label = QLabel("القسم:")
        section_filter_label.setFont(QFont("Calibri", 12, QFont.Bold))
        section_filter_label.setStyleSheet("color: #495057; border: none;")
        
        self.section_filter_combo = QComboBox()
        self.section_filter_combo.setFont(QFont("Calibri", 12, QFont.Bold))
        self.section_filter_combo.setMinimumWidth(150)
        self.section_filter_combo.setStyleSheet("""
            QComboBox {
                background-color: white;
                border: 2px solid #ced4da;
                border-radius: 5px;
                padding: 5px;
                font-weight: bold;
            }
            QComboBox:focus {
                border: 2px solid #007bff;
            }
            QComboBox::drop-down {
                border: none;
                width: 20px;
            }
            QComboBox::down-arrow {
                image: none;
                border-left: 5px solid transparent;
                border-right: 5px solid transparent;
                border-top: 5px solid #495057;
                margin-right: 5px;
            }
        """)
        self.section_filter_combo.currentTextChanged.connect(self.on_section_filter_changed)
        
        # زر إعادة تعيين التصفية
        reset_filter_btn = QPushButton("🔄 إعادة تعيين")
        reset_filter_btn.setFont(QFont("Calibri", 12, QFont.Bold))
        reset_filter_btn.setMaximumHeight(35)
        reset_filter_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #6c757d,
                    stop: 1 #495057
                );
                color: white;
                border: none;
                border-radius: 5px;
                padding: 5px 15px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #868e96,
                    stop: 1 #6c757d
                );
            }
            QPushButton:pressed {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #495057,
                    stop: 1 #343a40
                );
            }
        """)
        reset_filter_btn.clicked.connect(self.reset_filters)
        
        # إضافة العناصر للتخطيط
        filter_layout.addWidget(filter_label)
        filter_layout.addWidget(section_filter_label)
        filter_layout.addWidget(self.section_filter_combo)
        filter_layout.addWidget(reset_filter_btn)
        filter_layout.addStretch()
        
        main_layout.addWidget(filter_frame)
        
        # إنشاء الجدول
        self.table = QTableWidget()
        self.setup_table()
        main_layout.addWidget(self.table)
        
        # ضبط المسافات بين العناصر
        main_layout.setSpacing(5)
        
        # ضبط الحد الأدنى للحجم للنافذة
        self.setMinimumSize(1200, 800)

    def create_action_button(self, text, color):
        """إنشاء زر عمليات منسق"""
        button = QPushButton(text)
        button.setFont(QFont("Calibri", 13, QFont.Bold))
        button.setMaximumHeight(43)
        button.setMinimumWidth(150)
        
        # تحويل لون hex إلى لون مظلم
        dark_color = self.darken_color(color, 50)
        light_color = self.lighten_color(color, 40)
        
        button.setStyleSheet(f"""
            QPushButton {{
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 {color},
                    stop: 1 {dark_color}
                );
                color: white;
                border: none;
                border-radius: 8px;
                padding: 8px 15px;
                font-weight: bold;
                border: 2px solid {dark_color};
            }}
            QPushButton:hover {{
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 {light_color},
                    stop: 1 {color}
                );
                border: 2px solid {color};
            }}
            QPushButton:pressed {{
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 {dark_color},
                    stop: 1 {self.darken_color(dark_color, 20)}
                );
                border: 2px solid {self.darken_color(dark_color, 20)};
            }}
        """)
        return button

    def lighten_color(self, color, amount):
        """تفتيح اللون"""
        if color.startswith('#'):
            color = color[1:]
        
        r = min(255, int(color[0:2], 16) + amount)
        g = min(255, int(color[2:4], 16) + amount)
        b = min(255, int(color[4:6], 16) + amount)
        
        return f"#{r:02x}{g:02x}{b:02x}"

    def darken_color(self, color, amount):
        """تغميق اللون"""
        if color.startswith('#'):
            color = color[1:]
        
        r = max(0, int(color[0:2], 16) - amount)
        g = max(0, int(color[2:4], 16) - amount)
        b = max(0, int(color[4:6], 16) - amount)
        
        return f"#{r:02x}{g:02x}{b:02x}"

    def setup_table(self):
        """إعداد الجدول"""
        # تعيين عناوين الأعمدة (إزالة عمودي عدد الأقساط ومبلغ القسط)
        columns = [
            "اختيار", "ID", "اسم التلميذ", "رمز التلميذ", "النوع", "رقم الهاتف الأول",
            "القسم", "إجمالي مبلغ التسجيل", "الواجب الشهري", "المبلغ النهائي الشهري"
        ]
        
        self.table.setColumnCount(len(columns))
        self.table.setHorizontalHeaderLabels(columns)
        
        # تطبيق أنماط احترافية للجدول (إزالة box-shadow)
        self.table.setStyleSheet("""
            QTableWidget {
                background-color: #ffffff;
                border: 2px solid #ddd;
                border-radius: 8px;
                gridline-color: #e0e0e0;
                font-family: 'Calibri';
                font-size: 13px;
                font-weight: bold;
                selection-background-color: #e3f2fd;
            }
            QHeaderView::section {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #3498db,
                    stop: 1 #2980b9
                );
                color: white;
                font-weight: bold;
                font-size: 16px;
                font-family: 'Calibri';
                padding: 10px;
                border: none;
                border-right: 1px solid #2980b9;
                text-align: center;
            }
            
            QHeaderView::section:hover {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #5dade2,
                    stop: 1 #3498db
                );
            }
            
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #f0f0f0;
                font-family: 'Calibri';
                font-size: 13px;
                font-weight: bold;
            }
            
            QTableWidget::item:selected {
                background-color: #bbdefb;
                color: #1976d2;
            }
            
            QTableWidget::item:hover {
                background-color: #e3f2fd;
            }
        """)
        
        # إعدادات الجدول
        self.table.setAlternatingRowColors(True)
        self.table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.table.setSelectionMode(QAbstractItemView.SingleSelection)
        self.table.setSortingEnabled(True)
        
        # تعيين ارتفاع رأس الجدول والصفوف بشكل صحيح
        header = self.table.horizontalHeader()
        header.setFixedHeight(60)  # ارتفاع الرأس (زيادة ليتناسب مع حجم الخط الأكبر)
        header.setSectionResizeMode(QHeaderView.Interactive)  # يسمح بتغيير حجم الأعمدة
        
        # تعيين ارتفاع الصفوف
        self.table.verticalHeader().setDefaultSectionSize(40)
        
        # إخفاء رؤوس الصفوف الجانبية لتوفير مساحة
        self.table.verticalHeader().setVisible(False)
        
        # تخصيص عرض الأعمدة
        header.setStretchLastSection(False)  # تغيير لـ False لتحكم أفضل
        
        # تعيين عرض مخصص للأعمدة (بعد إزالة العمودين)
        self.table.setColumnWidth(0, 70)   # اختيار
        self.table.setColumnWidth(1, 100)   # ID
        self.table.setColumnWidth(2, 200)  # اسم التلميذ
        self.table.setColumnWidth(3, 150)  # رمز التلميذ
        self.table.setColumnWidth(4, 70)   # النوع
        self.table.setColumnWidth(5, 120)  # رقم الهاتف الأول
        self.table.setColumnWidth(6, 150)  # القسم
        self.table.setColumnWidth(7, 140)  # إجمالي مبلغ التسجيل
        self.table.setColumnWidth(8, 120)  # الواجب الشهري
        self.table.setColumnWidth(9, 150)  # المبلغ النهائي الشهري
        
        # تفعيل التمدد للعمود الأخير فقط
        header.setStretchLastSection(True)

    def load_filter_options(self):
        """تحميل خيارات التصفية (الأقسام فقط)"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # تحميل الأقسام من جدول_المواد_والاقسام للتصفية
            cursor.execute("SELECT DISTINCT القسم FROM جدول_المواد_والاقسام WHERE القسم IS NOT NULL ORDER BY القسم")
            sections = [row[0] for row in cursor.fetchall()]
            print(f"✅ تم تحميل {len(sections)} قسم من جدول_المواد_والاقسام للتصفية")

            # تحديث قائمة الأقسام
            self.section_filter_combo.blockSignals(True)
            self.section_filter_combo.clear()
            self.section_filter_combo.addItem("اختر القسم")
            if sections:
                self.section_filter_combo.addItems(sections)
            else:
                self.section_filter_combo.addItem("لا توجد أقسام متاحة")
                self.section_filter_combo.setEnabled(False)
            self.section_filter_combo.setCurrentIndex(0)
            self.section_filter_combo.blockSignals(False)

            conn.close()

        except Exception as e:
            print(f"خطأ في تحميل خيارات التصفية: {str(e)}")





    def on_section_filter_changed(self):
        """معالج تغيير تصفية القسم"""
        # تطبيق التصفية مباشرة عند تغيير القسم
        self.apply_filters()

    def apply_filters(self):
        """تطبيق التصفية بناءً على القسم المحدد"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # بناء الاستعلام مع التصفية
            base_query = """
                SELECT id, اسم_التلميذ, رمز_التلميذ, النوع, رقم_الهاتف_الأول,
                       القسم,
                       اجمالي_مبلغ_التسجيل,
                       الواجب_الشهري, المبلغ_النهائي_الشهري
                FROM جدول_البيانات
            """

            # تطبيق تصفية القسم
            selected_section = self.section_filter_combo.currentText()
            if selected_section and selected_section != "اختر القسم":
                # عرض الطلاب في القسم المحدد فقط
                query = base_query + " WHERE القسم = ? ORDER BY id DESC"
                cursor.execute(query, (selected_section,))
                records = cursor.fetchall()
                filter_info = f" - القسم: {selected_section}"
            else:
                # عرض جميع الطلاب إذا لم يتم اختيار قسم
                query = base_query + " ORDER BY id DESC"
                cursor.execute(query)
                records = cursor.fetchall()
                filter_info = " - جميع الأقسام"

            # تعيين عدد الصفوف
            self.table.setRowCount(len(records))

            # ملء الجدول بالبيانات (مع مربع الاختيار)
            for row_index, record in enumerate(records):
                # إضافة مربع اختيار في العمود الأول
                checkbox_item = QTableWidgetItem()
                checkbox_item.setFlags(Qt.ItemIsUserCheckable | Qt.ItemIsEnabled)
                checkbox_item.setCheckState(Qt.Unchecked)
                checkbox_item.setTextAlignment(Qt.AlignCenter)
                self.table.setItem(row_index, 0, checkbox_item)

                # ملء باقي الأعمدة (بدءاً من العمود 1)
                for col_index, value in enumerate(record):
                    # معالجة القيم الخاصة للأعمدة المالية
                    if col_index in [6, 7, 8] and value:  # الأعمدة المالية
                        display_value = f"{float(value):.2f} درهم" if value else "0.00 درهم"
                    else:
                        display_value = str(value) if value is not None else ""

                    item = QTableWidgetItem(display_value)
                    item.setTextAlignment(Qt.AlignCenter)
                    item.setFont(QFont("Calibri", 13, QFont.Bold))

                    # تلوين بعض الأعمدة
                    if col_index == 0:  # ID (الآن في العمود 1)
                        item.setBackground(QColor("#ecf0f1"))
                    elif col_index == 1:  # اسم التلميذ (الآن في العمود 2)
                        item.setForeground(QColor("#2c3e50"))
                    elif col_index in [6, 7, 8]:  # الأعمدة المالية
                        item.setBackground(QColor("#e8f5e8"))
                        item.setForeground(QColor("#27ae60"))

                    # وضع العنصر في العمود المناسب (col_index + 1 للإزاحة)
                    self.table.setItem(row_index, col_index + 1, item)

            conn.close()

            # تحديث عنوان النافذة ليشمل عدد السجلات
            self.setWindowTitle(f"عرض بيانات الطلاب{filter_info} - إجمالي السجلات: {len(records)}")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تطبيق التصفية: {str(e)}")

    def reset_filters(self):
        """إعادة تعيين جميع التصفيات"""
        self.section_filter_combo.blockSignals(True)
        self.section_filter_combo.setCurrentIndex(0)  # اختر القسم
        self.section_filter_combo.blockSignals(False)

        # تطبيق التصفية (عرض جميع الطلاب)
        self.apply_filters()

    def refresh_data(self):
        """تحديث البيانات"""
        # تحديث خيارات التصفية أولاً
        current_section = self.section_filter_combo.currentText()

        self.load_filter_options()

        # استعادة التحديد السابق إذا كان متاحاً
        if current_section and current_section != "اختر القسم":
            section_index = self.section_filter_combo.findText(current_section)
            if section_index >= 0:
                self.section_filter_combo.setCurrentIndex(section_index)

        self.apply_filters()

    def get_selected_student_id(self):
        """الحصول على ID التلميذ المحدد"""
        selected_ids = []
        for row in range(self.table.rowCount()):
            checkbox_item = self.table.item(row, 0)
            if checkbox_item and checkbox_item.checkState() == Qt.Checked:
                # ID موجود في العمود الثاني (فهرس 1)
                id_item = self.table.item(row, 1)
                if id_item:
                    try:
                        student_id = int(id_item.text())
                        selected_ids.append(student_id)
                    except ValueError:
                        continue
        return selected_ids

    def handle_registration(self):
        """التعامل مع التسجيل وإعادة التسجيل"""
        try:
            selected_ids = self.get_selected_student_id()
            
            if len(selected_ids) == 0:
                # الحالة الأولى: لا يوجد تلميذ محدد - فتح النافذة في وضع الإضافة
                self.open_registration_window_add_mode()
                
            elif len(selected_ids) == 1:
                student_id = selected_ids[0]
                
                # إظهار مربع حوار للاختيار بين التعديل أو إضافة قسم جديد
                from PyQt5.QtWidgets import QDialog, QVBoxLayout, QHBoxLayout, QPushButton, QLabel
                
                choice_dialog = QDialog(self)
                choice_dialog.setWindowTitle("اختر نوع العملية")
                choice_dialog.setModal(True)
                choice_dialog.setLayoutDirection(Qt.RightToLeft)
                choice_dialog.resize(400, 200)
                
                layout = QVBoxLayout(choice_dialog)
                
                # عنوان الحوار
                title_label = QLabel("اختر العملية المطلوبة:")
                title_label.setFont(QFont("Calibri", 14, QFont.Bold))
                title_label.setStyleSheet("color: #2c3e50; margin: 10px;")
                layout.addWidget(title_label)
                
                # الأزرار
                button_layout = QHBoxLayout()
                
                # زر تعديل البيانات الحالية
                edit_btn = QPushButton("✏️ تعديل البيانات الحالية")
                edit_btn.setFont(QFont("Calibri", 12, QFont.Bold))
                edit_btn.setStyleSheet("""
                    QPushButton {
                        background-color: #3498db;
                        color: white;
                        border: none;
                        border-radius: 8px;
                        padding: 10px 15px;
                        margin: 5px;
                    }
                    QPushButton:hover {
                        background-color: #2980b9;
                    }
                """)
                edit_btn.clicked.connect(lambda: self.handle_edit_choice(choice_dialog, student_id))
                
                # زر إضافة قسم جديد
                add_section_btn = QPushButton("➕ إضافة قسم جديد")
                add_section_btn.setFont(QFont("Calibri", 12, QFont.Bold))
                add_section_btn.setStyleSheet("""
                    QPushButton {
                        background-color: #27ae60;
                        color: white;
                        border: none;
                        border-radius: 8px;
                        padding: 10px 15px;
                        margin: 5px;
                    }
                    QPushButton:hover {
                        background-color: #229954;
                    }
                """)
                add_section_btn.clicked.connect(lambda: self.handle_add_section_choice(choice_dialog, student_id))
                
                # زر الإلغاء
                cancel_btn = QPushButton("❌ إلغاء")
                cancel_btn.setFont(QFont("Calibri", 12, QFont.Bold))
                cancel_btn.setStyleSheet("""
                    QPushButton {
                        background-color: #e74c3c;
                        color: white;
                        border: none;
                        border-radius: 8px;
                        padding: 10px 15px;
                        margin: 5px;
                    }
                    QPushButton:hover {
                        background-color: #c0392b;
                    }
                """)
                cancel_btn.clicked.connect(choice_dialog.reject)
                
                button_layout.addWidget(edit_btn)
                button_layout.addWidget(add_section_btn)
                button_layout.addWidget(cancel_btn)
                
                layout.addLayout(button_layout)
                
                # عرض الحوار
                choice_dialog.exec_()
                
            else:
                # أكثر من تلميذ محدد
                QMessageBox.warning(
                    self, 
                    "تحذير", 
                    "يرجى تحديد تلميذ واحد فقط أو عدم تحديد أي تلميذ للإضافة الجديدة."
                )
                
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء فتح نافذة التسجيل: {str(e)}")

    def generate_next_student_code(self):
        """إنشاء رمز التلميذ التالي تلقائياً"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # البحث عن أعلى رمز تلميذ موجود
            cursor.execute("""
                SELECT رمز_التلميذ FROM جدول_البيانات 
                WHERE رمز_التلميذ IS NOT NULL AND رمز_التلميذ != ''
                ORDER BY CAST(SUBSTR(رمز_التلميذ, 2) AS INTEGER) DESC 
                LIMIT 1
            """)
            
            result = cursor.fetchone()
            conn.close()
            
            if result and result[0]:
                # استخراج الرقم من رمز التلميذ الموجود (بعد حرف P)
                current_code = result[0]
                if current_code.startswith('P') and len(current_code) > 1:
                    try:
                        current_number = int(current_code[1:])
                        next_number = current_number + 1
                        return f"P{next_number}"
                    except ValueError:
                        # في حالة وجود رمز غير صالح، ابدأ من P10000
                        return "P10000"
                else:
                    return "P10000"
            else:
                # الجدول فارغ أو لا يوجد أرقام صحيحة
                return "P10000"
                
        except Exception as e:
            print(f"خطأ في إنشاء رمز التلميذ: {str(e)}")
            return "P10000"

    def open_registration_window_add_mode(self):
        """فتح نافذة التسجيل في وضع الإضافة"""
        try:
            # استيراد النافذة المطلوبة
            from sub232_window import MonthlyDutiesWindow
            
            # إنشاء وفتح النافذة في وضع الإضافة
            self.registration_window = MonthlyDutiesWindow(
                parent=self, 
                db_path=self.db_path
            )
            
            # إضافة زر الحفظ في تبويب الواجبات الشهرية
            self.add_save_button_to_monthly_tab(is_update=False)
            
            # تعيين وضع الإضافة
            self.registration_window.current_student_id = None
            
            # إنشاء رمز التلميذ التلقائي وتعبئته
            next_student_code = self.generate_next_student_code()
            if hasattr(self.registration_window, 'student_code_input'):
                self.registration_window.student_code_input.setText(next_student_code)
            
            # التأكد من أن النافذة في وضع الإضافة
            if hasattr(self.registration_window, 'setup_add_mode'):
                self.registration_window.setup_add_mode()
            
            # تغيير عنوان النافذة
            self.registration_window.setWindowTitle("إضافة تلميذ جديد")
            
            # إظهار النافذة
            self.registration_window.show()
            self.registration_window.raise_()
            self.registration_window.activateWindow()
            
        except ImportError:
            QMessageBox.critical(
                self, 
                "خطأ", 
                "لا يمكن العثور على نافذة التسجيل (sub232_window.py)."
            )
        except Exception as e:
            QMessageBox.critical(
                self, 
                "خطأ", 
                f"فشل في فتح نافذة التسجيل: {str(e)}"
            )

    def open_registration_window_edit_mode(self, student_id):
        """فتح نافذة التسجيل لعرض/تعديل بيانات التلميذ"""
        try:
            # التحقق من وجود التلميذ في قاعدة البيانات
            if not self.student_exists(student_id):
                QMessageBox.warning(
                    self, 
                    "تحذير", 
                    f"لا يمكن العثور على التلميذ بالمعرف: {student_id}"
                )
                return
            
            # استيراد النافذة المطلوبة
            from sub232_window import MonthlyDutiesWindow
            
            # إنشاء وفتح النافذة
            self.registration_window = MonthlyDutiesWindow(
                parent=self, 
                db_path=self.db_path
            )
            
            # إضافة زر التحديث في تبويب الواجبات الشهرية
            self.add_save_button_to_monthly_tab(is_update=True)
            
            # تحميل بيانات التلميذ المحدد
            self.load_student_data_to_window(student_id)
            
            # تعيين ID للتحديث
            self.registration_window.current_student_id = student_id
            
            # تغيير عنوان النافذة
            self.registration_window.setWindowTitle(f"تعديل بيانات التلميذ - ID: {student_id}")
            
            # إظهار النافذة
            self.registration_window.show()
            self.registration_window.raise_()
            self.registration_window.activateWindow()
            
        except ImportError:
            QMessageBox.critical(
                self, 
                "خطأ", 
                "لا يمكن العثور على نافذة التسجيل (sub232_window.py)."
            )
        except Exception as e:
            QMessageBox.critical(
                self, 
                "خطأ", 
                f"فشل في فتح نافذة التسجيل: {str(e)}"
            )

    def add_save_button_to_monthly_tab(self, is_update=False):
        """إضافة زر الحفظ/التحديث إلى تبويب الواجبات الشهرية"""
        try:
            window = self.registration_window
            
            # البحث عن تبويب الواجبات الشهرية
            tab_widget = window.tab_widget
            monthly_tab_index = -1
            
            for i in range(tab_widget.count()):
                if "الواجبات الشهرية" in tab_widget.tabText(i):
                    monthly_tab_index = i
                    break
            
            if monthly_tab_index == -1:
                return
            
            # الحصول على تبويب الواجبات الشهرية
            monthly_tab = tab_widget.widget(monthly_tab_index)
            layout = monthly_tab.layout()
            
            # إنشاء إطار الأزرار
            from PyQt5.QtWidgets import QHBoxLayout
            buttons_layout = QHBoxLayout()
            buttons_layout.setSpacing(15)
            
            # إنشاء الزر المناسب
            if is_update:
                save_button = window.create_styled_button("🔄 تحديث البيانات", "#2E7D32")
                save_button.clicked.connect(lambda: self.update_student_data())
            else:
                save_button = window.create_styled_button("💾 حفظ البيانات الجديدة", "#2E7D32")
                save_button.clicked.connect(lambda: self.save_new_student_data())
            
            # إضافة الزر للتخطيط
            buttons_layout.addStretch()
            buttons_layout.addWidget(save_button)
            buttons_layout.addStretch()
            
            # إضافة التخطيط إلى التبويب
            layout.addLayout(buttons_layout)
            
            # حفظ مرجع للزر
            window.save_button = save_button
            
        except Exception as e:
            print(f"خطأ في إضافة زر الحفظ: {str(e)}")

    def student_exists(self, student_id):
        """التحقق من وجود التلميذ في قاعدة البيانات"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute("SELECT COUNT(*) FROM جدول_البيانات WHERE id = ?", (student_id,))
            count = cursor.fetchone()[0]
            
            conn.close()
            return count > 0
            
        except Exception:
            return False

    def load_student_data_to_window(self, student_id):
        """تحميل بيانات التلميذ إلى النافذة"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # جلب بيانات التلميذ
            cursor.execute("SELECT * FROM جدول_البيانات WHERE id = ?", (student_id,))
            record = cursor.fetchone()
            conn.close()
            
            if record and hasattr(self, 'registration_window'):
                window = self.registration_window
                
                # ملء الحقول ببيانات التلميذ
                window.student_name_input.setText(record[1] or "")
                window.student_code_input.setText(record[2] or "")
                
                if record[3]:  # النوع
                    gender_index = window.gender_combo.findText(record[3])
                    if gender_index >= 0:
                        window.gender_combo.setCurrentIndex(gender_index)
                
                window.phone_input.setText(record[4] or "")
                window.phone2_input.setText(record[5] or "")
                window.address_input.setPlainText(record[6] or "")
                
                # معلومات التمدرس
                if record[7]:  # اسم المجموعة
                    group_index = window.group_combo.findText(record[7])
                    if group_index >= 0:
                        window.group_combo.setCurrentIndex(group_index)
                
                if record[8]:  # القسم
                    window.section_combo.setCurrentText(record[8])
                
                if record[9]:  # المؤسسة الأصلية
                    window.institution_combo.setCurrentText(record[9])
                
                # واجبات التسجيل - إجمالي المبلغ فقط
                if record[10]:  # إجمالي مبلغ التسجيل
                    window.total_amount_input.setText(str(record[10]))
                
                # الواجبات الشهرية
                if record[13]:  # الواجب الشهري
                    window.monthly_duty_input.setText(str(record[13]))
                
                # الأشهر المحددة
                if record[14]:
                    try:
                        selected_months = json.loads(record[14])
                        for month, checkbox in window.month_checkboxes.items():
                            checkbox.setChecked(month in selected_months)
                    except:
                        pass
                
                # تحديث الحسابات
                window.calculate_total_monthly()
                
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل بيانات التلميذ: {str(e)}")

    def save_new_student_data(self):
        """حفظ بيانات تلميذ جديد"""
        try:
            window = self.registration_window
            
            # التحقق من البيانات الأساسية
            if not window.student_name_input.text().strip():
                QMessageBox.warning(self, "تحذير", "يرجى إدخال اسم التلميذ.")
                return
                
            if not window.student_code_input.text().strip():
                QMessageBox.warning(self, "تحذير", "يرجى إدخال رمز التلميذ.")
                return
            
            # حفظ البيانات في قاعدة البيانات
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # جمع البيانات
            selected_months = []
            for month, checkbox in window.month_checkboxes.items():
                if checkbox.isChecked():
                    selected_months.append(month)
            
            # حساب القيم
            total_amount = float(window.total_amount_input.text() or 0)
            monthly_duty = float(window.monthly_duty_input.text() or 0)
            total_monthly = monthly_duty * len(selected_months)
            
            # إدراج السجل الجديد
            cursor.execute('''
                INSERT INTO جدول_البيانات 
                (اسم_التلميذ, رمز_التلميذ, النوع, رقم_الهاتف_الأول, رقم_الهاتف_الثاني, ملاحظات,
                 اسم_المجموعة, القسم, المؤسسة_الاصلية,
                 اجمالي_مبلغ_التسجيل, عدد_الاقساط, مبلغ_القسط,
                 الواجب_الشهري, الاشهر_المحددة, المبلغ_النهائي_الشهري)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                window.student_name_input.text().strip(),
                window.student_code_input.text().strip(),
                window.gender_combo.currentText(),
                window.phone_input.text().strip(),
                window.phone2_input.text().strip(),
                window.address_input.toPlainText().strip(),
                window.group_combo.currentText(),
                window.section_combo.currentText(),
                window.institution_combo.currentText(),
                total_amount,
                1,  # قيمة افتراضية لعدد الأقساط
                total_amount,  # مبلغ القسط = إجمالي المبلغ (قسط واحد)
                monthly_duty,
                json.dumps(selected_months),
                total_monthly
            ))
            
            conn.commit()
            conn.close()
            
            QMessageBox.information(self, "نجح", "تم حفظ بيانات التلميذ الجديد بنجاح.")
            
            # تحديث الجدول
            self.refresh_data()
            
            # إغلاق النافذة
            window.close()
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في حفظ البيانات: {str(e)}")

    def update_student_data(self):
        """تحديث بيانات التلميذ الموجود"""
        try:
            window = self.registration_window
            student_id = window.current_student_id
            
            if not student_id:
                QMessageBox.warning(self, "تحذير", "لا يمكن تحديد التلميذ للتحديث.")
                return
            
            # التحقق من البيانات الأساسية
            if not window.student_name_input.text().strip():
                QMessageBox.warning(self, "تحذير", "يرجى إدخال اسم التلميذ.")
                return
            
            # تحديث البيانات في قاعدة البيانات
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # جمع البيانات
            selected_months = []
            for month, checkbox in window.month_checkboxes.items():
                if checkbox.isChecked():
                    selected_months.append(month)
            
            # حساب القيم
            total_amount = float(window.total_amount_input.text() or 0)
            monthly_duty = float(window.monthly_duty_input.text() or 0)
            total_monthly = monthly_duty * len(selected_months)
            
            # تحديث السجل
            cursor.execute('''
                UPDATE جدول_البيانات 
                SET اسم_التلميذ = ?, رمز_التلميذ = ?, النوع = ?, رقم_الهاتف_الأول = ?, 
                    رقم_الهاتف_الثاني = ?, ملاحظات = ?,
                    اسم_المجموعة = ?, القسم = ?, المؤسسة_الاصلية = ?,
                    اجمالي_مبلغ_التسجيل = ?, عدد_الاقساط = ?, مبلغ_القسط = ?,
                    الواجب_الشهري = ?, الاشهر_المحددة = ?, المبلغ_النهائي_الشهري = ?,
                    تاريخ_التحديث = CURRENT_TIMESTAMP
                WHERE id = ?
            ''', (
                window.student_name_input.text().strip(),
                window.student_code_input.text().strip(),
                window.gender_combo.currentText(),
                window.phone_input.text().strip(),
                window.phone2_input.text().strip(),
                window.address_input.toPlainText().strip(),
                window.group_combo.currentText(),
                window.section_combo.currentText(),
                window.institution_combo.currentText(),
                total_amount,
                1,  # قيمة افتراضية لعدد الأقساط
                total_amount,  # مبلغ القسط = إجمالي المبلغ (قسط واحد)
                monthly_duty,
                json.dumps(selected_months),
                total_monthly,
                student_id
            ))
            
            conn.commit()
            conn.close()
            
            QMessageBox.information(self, "نجح", "تم تحديث بيانات التلميذ بنجاح.")
            
            # تحديث الجدول
            self.refresh_data()
            
            # إغلاق النافذة
            window.close()
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحديث البيانات: {str(e)}")

    def handle_monthly_duties(self):
        """التعامل مع أداء الواجبات الشهرية"""
        try:
            selected_ids = self.get_selected_student_id()
            
            if len(selected_ids) == 0:
                QMessageBox.warning(
                    self, 
                    "تحذير", 
                    "يرجى تحديد تلميذ واحد لإدارة واجباته الشهرية."
                )
                return
                
            elif len(selected_ids) == 1:
                # تلميذ واحد محدد - فتح نافذة إدارة الواجبات
                student_id = selected_ids[0]
                self.open_monthly_duties_window(student_id)
                
            else:
                # أكثر من تلميذ محدد
                QMessageBox.warning(
                    self, 
                    "تحذير", 
                    "يرجى تحديد تلميذ واحد فقط لإدارة واجباته الشهرية."
                )
                
        except Exception as e:
            QMessageBox.critical(
                self, 
                "خطأ", 
                f"حدث خطأ أثناء فتح نافذة الواجبات الشهرية: {str(e)}"
            )

    def open_monthly_duties_window(self, student_id):
        """فتح نافذة إدارة الواجبات الشهرية"""
        try:
            # التحقق من وجود التلميذ في قاعدة البيانات
            if not self.student_exists(student_id):
                QMessageBox.warning(
                    self, 
                    "تحذير", 
                    f"لا يمكن العثور على التلميذ بالمعرف: {student_id}"
                )
                return
            
            # محاولة استيراد وفتح نافذة الواجبات الشهرية
            try:
                from monthly_duties_window import MonthlyDutiesManagementWindow
                
                # إنشاء وفتح النافذة
                self.monthly_duties_window = MonthlyDutiesManagementWindow(
                    parent=self,
                    db_path=self.db_path,
                    student_id=student_id
                )
                
                # إظهار النافذة
                self.monthly_duties_window.show()
                self.monthly_duties_window.raise_()
                self.monthly_duties_window.activateWindow()
                
            except ImportError:
                QMessageBox.critical(
                    self, 
                    "خطأ في الاستيراد", 
                    "لا يمكن العثور على نافذة الواجبات الشهرية.\n"
                    "تأكد من وجود ملف monthly_duties_window.py"
                )
                return
                
        except Exception as e:
            QMessageBox.critical(
                self, 
                "خطأ", 
                f"فشل في فتح نافذة الواجبات الشهرية: {str(e)}"
            )

    def handle_detailed_report(self):
        """التعامل مع طباعة التقرير المفصل"""
        try:
            selected_ids = self.get_selected_student_id()
            
            if len(selected_ids) == 0:
                QMessageBox.warning(
                    self, 
                    "تحذير", 
                    "يرجى تحديد تلميذ واحد لإنشاء تقرير مفصل عنه."
                )
                return
                
            elif len(selected_ids) == 1:
                # تلميذ واحد محدد - إنشاء تقرير PDF مباشر
                student_id = selected_ids[0]
                self.create_pdf_detailed_report(student_id)
                
            else:
                # أكثر من تلميذ محدد
                QMessageBox.warning(
                    self, 
                    "تحذير", 
                    "يرجى تحديد تلميذ واحد فقط لإنشاء التقرير المفصل."
                )
                
        except Exception as e:
            QMessageBox.critical(
                self, 
                "خطأ", 
                f"حدث خطأ أثناء إنشاء التقرير المفصل: {str(e)}"
            )

    def create_pdf_detailed_report(self, student_id):
        """إنشاء تقرير PDF مفصل باستخدام print101"""
        try:
            # التحقق من وجود التلميذ في قاعدة البيانات
            if not self.student_exists(student_id):
                QMessageBox.warning(
                    self, 
                    "تحذير", 
                    f"لا يمكن العثور على التلميذ بالمعرف: {student_id}"
                )
                return
            
            # محاولة استيراد وتشغيل تقرير PDF
            try:
                from print101 import print_student_detailed_report
            except ImportError:
                QMessageBox.critical(
                    self, 
                    "خطأ", 
                    "لا يمكن العثور على وحدة التقرير.\n"
                    "تأكد من وجود ملف print101.py"
                )
                return
            
            # إنشاء التقرير
            try:
                success, output_path, message = print_student_detailed_report(
                    parent=self, 
                    student_id=student_id
                )
                
                if success:
                    QMessageBox.information(
                        self, 
                        "نجح", 
                        f"تم إنشاء التقرير المفصل بنجاح!\n\n"
                        f"المسار: {output_path}\n\n{message}"
                    )
                else:
                    QMessageBox.critical(
                        self, 
                        "خطأ", 
                        f"فشل في إنشاء التقرير المفصل.\n\nالسبب: {message}"
                    )
                    
            except Exception as creation_error:
                QMessageBox.critical(
                    self, 
                    "خطأ", 
                    f"حدث خطأ أثناء إنشاء التقرير: {str(creation_error)}"
                )
                return
            
        except Exception as e:
            QMessageBox.critical(
                self,
                "خطأ",
                f"فشل في إنشاء التقرير المفصل: {str(e)}"
            )

    def handle_section_monthly_report(self):
        """التعامل مع تقرير القسم الشهري"""
        try:
            # الحصول على القسم المحدد من التصفية
            selected_section = self.section_filter_combo.currentText()

            if not selected_section or selected_section == "اختر القسم":
                QMessageBox.warning(
                    self,
                    "تحذير",
                    "يرجى تحديد قسم من قائمة التصفية أولاً لإنشاء التقرير الشهري."
                )
                return

            # فتح نافذة اختيار الشهر
            self.show_month_selection_dialog(selected_section)

        except Exception as e:
            QMessageBox.critical(
                self,
                "خطأ",
                f"حدث خطأ أثناء إنشاء تقرير القسم الشهري: {str(e)}"
            )

    def show_month_selection_dialog(self, section):
        """إظهار نافذة اختيار الشهر"""
        try:
            from PyQt5.QtWidgets import QDialog, QVBoxLayout, QHBoxLayout, QPushButton, QLabel, QComboBox

            dialog = QDialog(self)
            dialog.setWindowTitle(f"اختيار الشهر لتقرير القسم: {section}")
            dialog.setModal(True)
            dialog.setLayoutDirection(Qt.RightToLeft)
            dialog.resize(400, 200)

            layout = QVBoxLayout(dialog)

            # عنوان الحوار
            title_label = QLabel(f"اختر الشهر لإنشاء تقرير القسم: {section}")
            title_label.setFont(QFont("Calibri", 14, QFont.Bold))
            title_label.setStyleSheet("color: #2c3e50; margin: 10px;")
            layout.addWidget(title_label)

            # قائمة الأشهر
            month_layout = QHBoxLayout()
            month_label = QLabel("الشهر:")
            month_label.setFont(QFont("Calibri", 12, QFont.Bold))

            month_combo = QComboBox()
            month_combo.setFont(QFont("Calibri", 12))
            months = [
                "يناير", "فبراير", "مارس", "أبريل", "مايو", "يونيو",
                "يوليو", "أغسطس", "سبتمبر", "أكتوبر", "نوفمبر", "ديسمبر"
            ]
            month_combo.addItems(months)

            # تحديد الشهر الحالي
            from datetime import datetime
            current_month = datetime.now().month - 1  # فهرس الشهر (0-11)
            month_combo.setCurrentIndex(current_month)

            month_layout.addWidget(month_label)
            month_layout.addWidget(month_combo)
            month_layout.addStretch()
            layout.addLayout(month_layout)

            # الأزرار
            button_layout = QHBoxLayout()

            # زر إنشاء التقرير
            create_btn = QPushButton("📊 إنشاء التقرير")
            create_btn.setFont(QFont("Calibri", 12, QFont.Bold))
            create_btn.setStyleSheet("""
                QPushButton {
                    background-color: #17a2b8;
                    color: white;
                    border: none;
                    border-radius: 8px;
                    padding: 10px 15px;
                    margin: 5px;
                }
                QPushButton:hover {
                    background-color: #138496;
                }
            """)
            create_btn.clicked.connect(lambda: self.create_section_monthly_report(
                dialog, section, month_combo.currentText()
            ))

            # زر الإلغاء
            cancel_btn = QPushButton("❌ إلغاء")
            cancel_btn.setFont(QFont("Calibri", 12, QFont.Bold))
            cancel_btn.setStyleSheet("""
                QPushButton {
                    background-color: #e74c3c;
                    color: white;
                    border: none;
                    border-radius: 8px;
                    padding: 10px 15px;
                    margin: 5px;
                }
                QPushButton:hover {
                    background-color: #c0392b;
                }
            """)
            cancel_btn.clicked.connect(dialog.reject)

            button_layout.addWidget(create_btn)
            button_layout.addWidget(cancel_btn)

            layout.addLayout(button_layout)

            # عرض الحوار
            dialog.exec_()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في إظهار نافذة اختيار الشهر: {str(e)}")

    def create_section_monthly_report(self, dialog, section, month):
        """إنشاء تقرير القسم الشهري"""
        try:
            dialog.accept()  # إغلاق النافذة

            # محاولة استيراد وحدة التقرير
            try:
                from print_section_monthly import print_section_monthly_report
            except ImportError:
                QMessageBox.critical(
                    self,
                    "خطأ",
                    "لا يمكن العثور على وحدة تقرير القسم الشهري.\n"
                    "تأكد من وجود ملف print_section_monthly.py"
                )
                return

            # إنشاء التقرير
            try:
                success, output_path, message = print_section_monthly_report(
                    parent=self,
                    section=section,
                    month=month
                )

                if success:
                    QMessageBox.information(
                        self,
                        "نجح",
                        f"تم إنشاء تقرير القسم الشهري بنجاح!\n\n"
                        f"القسم: {section}\n"
                        f"الشهر: {month}\n"
                        f"المسار: {output_path}\n\n{message}"
                    )
                else:
                    QMessageBox.critical(
                        self,
                        "خطأ",
                        f"فشل في إنشاء تقرير القسم الشهري.\n\nالسبب: {message}"
                    )

            except Exception as creation_error:
                QMessageBox.critical(
                    self,
                    "خطأ",
                    f"حدث خطأ أثناء إنشاء التقرير: {str(creation_error)}"
                )
                return

        except Exception as e:
            QMessageBox.critical(
                self,
                "خطأ",
                f"فشل في إنشاء تقرير القسم الشهري: {str(e)}"
            )

    def handle_section_yearly_report(self):
        """التعامل مع تقرير القسم السنوي"""
        try:
            # الحصول على القسم المحدد من التصفية
            selected_section = self.section_filter_combo.currentText()

            if not selected_section or selected_section == "اختر القسم":
                QMessageBox.warning(
                    self,
                    "تحذير",
                    "يرجى تحديد قسم من قائمة التصفية أولاً لإنشاء التقرير السنوي."
                )
                return

            # فتح نافذة اختيار السنة
            self.show_year_selection_dialog(selected_section)

        except Exception as e:
            QMessageBox.critical(
                self,
                "خطأ",
                f"حدث خطأ أثناء إنشاء تقرير القسم السنوي: {str(e)}"
            )

    def show_year_selection_dialog(self, section):
        """إظهار نافذة اختيار السنة"""
        try:
            from PyQt5.QtWidgets import QDialog, QVBoxLayout, QHBoxLayout, QPushButton, QLabel, QComboBox
            from datetime import datetime

            dialog = QDialog(self)
            dialog.setWindowTitle(f"اختيار السنة لتقرير القسم: {section}")
            dialog.setModal(True)
            dialog.setLayoutDirection(Qt.RightToLeft)
            dialog.resize(400, 200)

            layout = QVBoxLayout(dialog)

            # عنوان الحوار
            title_label = QLabel(f"اختر السنة لإنشاء تقرير القسم: {section}")
            title_label.setFont(QFont("Calibri", 14, QFont.Bold))
            title_label.setStyleSheet("color: #2c3e50; margin: 10px;")
            layout.addWidget(title_label)

            # قائمة السنوات
            year_layout = QHBoxLayout()
            year_label = QLabel("السنة:")
            year_label.setFont(QFont("Calibri", 12, QFont.Bold))
            year_combo = QComboBox()
            year_combo.setFont(QFont("Calibri", 12, QFont.Bold))

            # إضافة السنوات (السنة الحالية و 5 سنوات سابقة)
            current_year = datetime.now().year
            for year in range(current_year, current_year - 6, -1):
                year_combo.addItem(str(year))

            year_layout.addWidget(year_label)
            year_layout.addWidget(year_combo)
            year_layout.addStretch()

            layout.addLayout(year_layout)

            # الأزرار
            button_layout = QHBoxLayout()

            # زر إنشاء التقرير
            create_btn = QPushButton("📈 إنشاء التقرير السنوي")
            create_btn.setFont(QFont("Calibri", 12, QFont.Bold))
            create_btn.setStyleSheet("""
                QPushButton {
                    background-color: #fd7e14;
                    color: white;
                    border: none;
                    border-radius: 8px;
                    padding: 10px 15px;
                    margin: 5px;
                }
                QPushButton:hover {
                    background-color: #e8650e;
                }
            """)
            create_btn.clicked.connect(lambda: self.create_section_yearly_report(
                dialog, section, year_combo.currentText()
            ))

            # زر الإلغاء
            cancel_btn = QPushButton("❌ إلغاء")
            cancel_btn.setFont(QFont("Calibri", 12, QFont.Bold))
            cancel_btn.setStyleSheet("""
                QPushButton {
                    background-color: #dc3545;
                    color: white;
                    border: none;
                    border-radius: 8px;
                    padding: 10px 15px;
                    margin: 5px;
                }
                QPushButton:hover {
                    background-color: #c82333;
                }
            """)
            cancel_btn.clicked.connect(dialog.reject)

            button_layout.addWidget(create_btn)
            button_layout.addWidget(cancel_btn)
            button_layout.addStretch()

            layout.addLayout(button_layout)

            # عرض النافذة
            dialog.exec_()

        except Exception as e:
            QMessageBox.critical(
                self,
                "خطأ",
                f"فشل في إظهار نافذة اختيار السنة: {str(e)}"
            )

    def create_section_yearly_report(self, dialog, section, year):
        """إنشاء تقرير القسم السنوي"""
        try:
            dialog.accept()  # إغلاق النافذة

            # محاولة استيراد وحدة التقرير
            try:
                from print_section_yearly import print_section_yearly_report
            except ImportError:
                QMessageBox.critical(
                    self,
                    "خطأ",
                    "لا يمكن العثور على وحدة تقرير القسم السنوي.\n"
                    "تأكد من وجود ملف print_section_yearly.py"
                )
                return

            # إنشاء التقرير
            try:
                success, output_path, message = print_section_yearly_report(
                    parent=self,
                    section=section,
                    year=year
                )

                if success:
                    QMessageBox.information(
                        self,
                        "نجح",
                        f"تم إنشاء تقرير القسم السنوي بنجاح!\n\n"
                        f"القسم: {section}\n"
                        f"السنة: {year}\n"
                        f"المسار: {output_path}\n\n{message}"
                    )
                else:
                    QMessageBox.critical(
                        self,
                        "خطأ",
                        f"فشل في إنشاء تقرير القسم السنوي.\n\nالسبب: {message}"
                    )

            except Exception as creation_error:
                QMessageBox.critical(
                    self,
                    "خطأ",
                    f"حدث خطأ أثناء إنشاء التقرير: {str(creation_error)}"
                )
                return

        except Exception as e:
            QMessageBox.critical(
                self,
                "خطأ",
                f"فشل في إنشاء تقرير القسم السنوي: {str(e)}"
            )

    def showEvent(self, event):
        """عند إظهار النافذة - تعظيمها تلقائياً"""
        super().showEvent(event)
        self.showMaximized()

    def handle_edit_choice(self, dialog, student_id):
        """التعامل مع اختيار تعديل البيانات الحالية"""
        dialog.accept()
        self.open_registration_window_edit_mode(student_id)
    
    def handle_add_section_choice(self, dialog, student_id):
        """التعامل مع اختيار إضافة قسم جديد"""
        dialog.accept()
        self.open_registration_window_add_section_mode(student_id)
    
    def get_student_info(self, student_id):
        """الحصول على معلومات التلميذ من قاعدة البيانات"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT اسم_التلميذ, رمز_التلميذ, النوع, رقم_الهاتف_الأول, رقم_الهاتف_الثاني
                FROM جدول_البيانات 
                WHERE id = ?
                LIMIT 1
            """, (student_id,))
            
            result = cursor.fetchone()
            conn.close()
            
            if result:
                return {
                    'name': result[0],
                    'code': result[1],
                    'gender': result[2],
                    'phone1': result[3],
                    'phone2': result[4]
                }
            return None
            
        except Exception as e:
            print(f"خطأ في الحصول على معلومات التلميذ: {str(e)}")
            return None
    
    def open_registration_window_add_section_mode(self, student_id):
        """فتح نافذة التسجيل لإضافة قسم جديد للتلميذ الموجود"""
        try:
            # الحصول على معلومات التلميذ الأساسية
            student_info = self.get_student_info(student_id)
            if not student_info:
                QMessageBox.warning(
                    self, 
                    "تحذير", 
                    f"لا يمكن العثور على معلومات التلميذ بالمعرف: {student_id}"
                )
                return
            
            # استيراد النافذة المطلوبة
            from sub232_window import MonthlyDutiesWindow
            
            # إنشاء وفتح النافذة في وضع إضافة قسم جديد
            self.registration_window = MonthlyDutiesWindow(
                parent=self, 
                db_path=self.db_path
            )
            
            # إضافة زر الحفظ في تبويب الواجبات الشهرية
            self.add_save_button_to_monthly_tab(is_update=False)
            
            # تعيين وضع إضافة قسم جديد
            self.registration_window.current_student_id = None  # لا نريد تحديث السجل الموجود
            self.registration_window.original_student_id = student_id  # حفظ معرف التلميذ الأصلي
            
            # التأكد من أن النافذة في وضع الإضافة أولاً (هذا سيمسح الحقول)
            if hasattr(self.registration_window, 'setup_add_mode'):
                self.registration_window.setup_add_mode()
            
            # تعبئة المعلومات الأساسية للتلميذ
            if hasattr(self.registration_window, 'student_name_input'):
                self.registration_window.student_name_input.setText(student_info['name'] or '')
            
            if hasattr(self.registration_window, 'student_code_input'):
                self.registration_window.student_code_input.setText(student_info['code'] or '')
            
            if hasattr(self.registration_window, 'gender_combo'):
                if student_info['gender']:
                    index = self.registration_window.gender_combo.findText(student_info['gender'])
                    if index >= 0:
                        self.registration_window.gender_combo.setCurrentIndex(index)
            
            if hasattr(self.registration_window, 'phone1_input'):
                self.registration_window.phone1_input.setText(student_info['phone1'] or '')
            
            if hasattr(self.registration_window, 'phone2_input'):
                self.registration_window.phone2_input.setText(student_info['phone2'] or '')
            
            # تغيير عنوان النافذة
            self.registration_window.setWindowTitle(f"إضافة قسم جديد للتلميذ: {student_info['name']}")
            
            # إضافة ملاحظة في النافذة
            if hasattr(self.registration_window, 'add_info_label'):
                info_text = f"🔄 إضافة قسم جديد للتلميذ: {student_info['name']} (رمز: {student_info['code']})"
                self.registration_window.add_info_label(info_text)
            
            # إظهار النافذة
            self.registration_window.show()
            self.registration_window.raise_()
            self.registration_window.activateWindow()
            
        except ImportError:
            QMessageBox.critical(
                self, 
                "خطأ", 
                "لا يمكن العثور على نافذة التسجيل (sub232_window.py)."
            )
        except Exception as e:
            QMessageBox.critical(
                self,
                "خطأ",
                f"فشل في فتح نافذة إضافة القسم الجديد: {str(e)}"
            )

    def handle_bulk_edit(self):
        """التعامل مع التعديل الجماعي للتلاميذ المحددين"""
        try:
            selected_ids = self.get_selected_student_id()

            if len(selected_ids) == 0:
                QMessageBox.warning(
                    self,
                    "تحذير",
                    "يرجى تحديد تلميذ واحد أو أكثر للتعديل الجماعي."
                )
                return
            elif len(selected_ids) == 1:
                QMessageBox.information(
                    self,
                    "معلومة",
                    "تم تحديد تلميذ واحد فقط. يمكنك استخدام زر 'التسجيل وإعادة التسجيل' للتعديل الفردي.\n"
                    "أو تحديد المزيد من التلاميذ للتعديل الجماعي."
                )
                return
            else:
                # أكثر من تلميذ محدد - فتح نافذة التعديل الجماعي
                self.open_bulk_edit_window(selected_ids)

        except Exception as e:
            QMessageBox.critical(
                self,
                "خطأ",
                f"حدث خطأ أثناء فتح نافذة التعديل الجماعي: {str(e)}"
            )

    def open_bulk_edit_window(self, selected_ids):
        """فتح نافذة التعديل الجماعي"""
        try:
            from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QPushButton,
                                       QLabel, QLineEdit, QComboBox, QCheckBox, QGroupBox,
                                       QGridLayout, QScrollArea)

            # إنشاء النافذة
            dialog = QDialog(self)
            dialog.setWindowTitle(f"التعديل الجماعي - {len(selected_ids)} تلميذ محدد")
            dialog.setModal(True)
            dialog.setLayoutDirection(Qt.RightToLeft)
            dialog.resize(700, 600)  # زيادة الحجم لاستيعاب المحتوى
            dialog.setMinimumSize(650, 550)  # تحديد حد أدنى للحجم
            dialog.setStyleSheet("""
                QDialog {
                    background-color: #f8f9fa;
                }
                QGroupBox {
                    font-weight: bold;
                    font-size: 14px;
                    border: 2px solid #dee2e6;
                    border-radius: 8px;
                    margin-top: 10px;
                    padding-top: 10px;
                }
                QGroupBox::title {
                    subcontrol-origin: margin;
                    left: 10px;
                    padding: 0 5px 0 5px;
                }
            """)

            layout = QVBoxLayout(dialog)

            # عنوان النافذة
            title_label = QLabel(f"🔄 التعديل الجماعي لـ {len(selected_ids)} تلميذ")
            title_label.setFont(QFont("Calibri", 16, QFont.Bold))
            title_label.setStyleSheet("color: #2c3e50; margin: 10px; text-align: center;")
            layout.addWidget(title_label)

            # مجموعة معلومات التمدرس
            study_group = QGroupBox("📚 معلومات التمدرس")
            study_layout = QGridLayout(study_group)

            # المجموعة الجديدة
            group_check = QCheckBox("تغيير المجموعة:")
            group_check.setFont(QFont("Calibri", 13, QFont.Bold))
            group_combo = QComboBox()
            group_combo.setFont(QFont("Calibri", 13, QFont.Bold))
            group_combo.setEnabled(False)
            group_check.toggled.connect(group_combo.setEnabled)

            # تحميل المجموعات المتاحة
            self.load_groups_to_combo(group_combo)

            study_layout.addWidget(group_check, 0, 0)
            study_layout.addWidget(group_combo, 0, 1)

            # القسم الجديد
            section_check = QCheckBox("تغيير القسم:")
            section_check.setFont(QFont("Calibri", 13, QFont.Bold))
            section_combo = QComboBox()
            section_combo.setFont(QFont("Calibri", 13, QFont.Bold))
            section_combo.setEnabled(False)
            section_check.toggled.connect(section_combo.setEnabled)

            # تحميل الأقسام المتاحة
            self.load_sections_to_combo(section_combo)

            study_layout.addWidget(section_check, 1, 0)
            study_layout.addWidget(section_combo, 1, 1)

            layout.addWidget(study_group)

            # مجموعة الواجبات المالية
            financial_group = QGroupBox("💰 الواجبات المالية")
            financial_layout = QGridLayout(financial_group)

            # واجبات التسجيل
            registration_check = QCheckBox("تغيير واجبات التسجيل:")
            registration_check.setFont(QFont("Calibri", 13, QFont.Bold))
            registration_input = QLineEdit()
            registration_input.setFont(QFont("Calibri", 13, QFont.Bold))
            registration_input.setPlaceholderText("مثال: 200 درهم ")
            registration_input.setStyleSheet("""
                QLineEdit {
                    padding: 8px;
                    border: 2px solid #ced4da;
                    border-radius: 5px;
                    background-color: white;
                }
                QLineEdit:focus {
                    border: 2px solid #007bff;
                }
                QLineEdit:disabled {
                    background-color: #f8f9fa;
                    color: #6c757d;
                }
            """)
            registration_input.setEnabled(False)
            registration_check.toggled.connect(registration_input.setEnabled)

            financial_layout.addWidget(registration_check, 0, 0)
            financial_layout.addWidget(registration_input, 0, 1)

            # الواجب الشهري
            monthly_check = QCheckBox("تغيير الواجب الشهري:")
            monthly_check.setFont(QFont("Calibri", 13, QFont.Bold))
            monthly_input = QLineEdit()
            monthly_input.setFont(QFont("Calibri", 13, QFont.Bold))
            monthly_input.setPlaceholderText("مثال: 200 درهم   ")
            monthly_input.setStyleSheet("""
                QLineEdit {
                    padding: 8px;
                    border: 2px solid #ced4da;
                    border-radius: 5px;
                    background-color: white;
                }
                QLineEdit:focus {
                    border: 2px solid #007bff;
                }
                QLineEdit:disabled {
                    background-color: #f8f9fa;
                    color: #6c757d;
                }
            """)
            monthly_input.setEnabled(False)
            monthly_check.toggled.connect(monthly_input.setEnabled)

            financial_layout.addWidget(monthly_check, 1, 0)
            financial_layout.addWidget(monthly_input, 1, 1)

            layout.addWidget(financial_group)

            # معلومات التلاميذ المحددين مع منطقة تمرير
            info_group = QGroupBox("👥 التلاميذ المحددين")
            info_layout = QVBoxLayout(info_group)

            # إنشاء منطقة تمرير للتلاميذ
            scroll_area = QScrollArea()
            scroll_area.setWidgetResizable(True)
            scroll_area.setMaximumHeight(150)  # تحديد ارتفاع أقصى لمنطقة التمرير
            scroll_area.setStyleSheet("""
                QScrollArea {
                    border: 1px solid #dee2e6;
                    border-radius: 5px;
                    background-color: white;
                }
            """)

            students_info = self.get_selected_students_info(selected_ids)
            info_text = "\n".join([f"• {info['name']} (رمز: {info['code']}) - القسم: {info['section']}"
                                 for info in students_info])

            info_label = QLabel(info_text)
            info_label.setFont(QFont("Calibri", 12))
            info_label.setStyleSheet("background-color: white; padding: 10px; border: none;")
            info_label.setWordWrap(True)

            scroll_area.setWidget(info_label)
            info_layout.addWidget(scroll_area)

            layout.addWidget(info_group)

            # إضافة مساحة مرنة لضمان ظهور الأزرار
            layout.addStretch()

            # الأزرار
            button_layout = QHBoxLayout()

            # زر التطبيق
            apply_btn = QPushButton("✅ تطبيق التغييرات")
            apply_btn.setFont(QFont("Calibri", 13, QFont.Bold))
            apply_btn.setStyleSheet("""
                QPushButton {
                    background-color: #28a745;
                    color: white;
                    border: none;
                    border-radius: 8px;
                    padding: 12px 20px;
                    margin: 5px;
                }
                QPushButton:hover {
                    background-color: #218838;
                }
            """)
            apply_btn.clicked.connect(lambda: self.apply_bulk_changes(
                dialog, selected_ids, group_check, group_combo, section_check, section_combo,
                registration_check, registration_input, monthly_check, monthly_input
            ))

            # زر الإلغاء
            cancel_btn = QPushButton("❌ إلغاء")
            cancel_btn.setFont(QFont("Calibri", 12, QFont.Bold))
            cancel_btn.setStyleSheet("""
                QPushButton {
                    background-color: #dc3545;
                    color: white;
                    border: none;
                    border-radius: 8px;
                    padding: 12px 20px;
                    margin: 5px;
                }
                QPushButton:hover {
                    background-color: #c82333;
                }
            """)
            cancel_btn.clicked.connect(dialog.reject)

            button_layout.addWidget(apply_btn)
            button_layout.addWidget(cancel_btn)

            layout.addLayout(button_layout)

            # عرض النافذة
            dialog.exec_()

        except Exception as e:
            QMessageBox.critical(
                self,
                "خطأ",
                f"فشل في فتح نافذة التعديل الجماعي: {str(e)}"
            )

    def load_groups_to_combo(self, combo):
        """تحميل المجموعات المتاحة من جدول المجموعات حصرياً"""
        try:
            print("🔍 بدء تحميل المجموعات للتعديل الجماعي...")
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # التحقق من وجود جدول المجموعات أولاً
            print("🔍 فحص وجود جدول المجموعات...")
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='المجموعات'")
            table_check = cursor.fetchone()

            if not table_check:
                print("❌ جدول المجموعات غير موجود")
                combo.clear()
                combo.addItem("جدول المجموعات غير موجود")
                combo.setEnabled(False)
                conn.close()
                return
            else:
                print("✅ جدول المجموعات موجود")

            # جلب المجموعات من جدول المجموعات فقط
            print("🔍 جلب المجموعات من جدول المجموعات...")
            cursor.execute("SELECT DISTINCT اسم_المجموعة FROM المجموعات WHERE اسم_المجموعة IS NOT NULL ORDER BY اسم_المجموعة")
            groups_result = cursor.fetchall()
            groups = [row[0] for row in groups_result]

            print(f"📋 المجموعات المجلبة من جدول المجموعات: {groups}")

            combo.clear()
            if groups:
                combo.addItem("اختر المجموعة الجديدة")
                combo.addItems(groups)
                combo.setEnabled(True)
                print(f"✅ تم تحميل {len(groups)} مجموعة من جدول المجموعات للتعديل الجماعي")
                print(f"📋 المجموعات المضافة للقائمة: {groups}")
            else:
                combo.addItem("لا توجد مجموعات متاحة")
                combo.setEnabled(False)
                print("⚠️ لا توجد مجموعات في جدول المجموعات")

            conn.close()

        except Exception as e:
            print(f"❌ خطأ في تحميل المجموعات من جدول المجموعات: {str(e)}")
            import traceback
            print(f"📋 تفاصيل الخطأ: {traceback.format_exc()}")
            combo.clear()
            combo.addItem("خطأ في تحميل المجموعات")
            combo.setEnabled(False)

    def load_sections_to_combo(self, combo):
        """تحميل الأقسام المتاحة من جدول_المواد_والاقسام"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # جلب الأقسام من جدول_المواد_والاقسام
            cursor.execute("SELECT DISTINCT القسم FROM جدول_المواد_والاقسام WHERE القسم IS NOT NULL ORDER BY القسم")
            sections = [row[0] for row in cursor.fetchall()]

            combo.clear()
            combo.addItem("اختر القسم الجديد")
            combo.addItems(sections)

            conn.close()

        except Exception as e:
            print(f"خطأ في تحميل الأقسام: {str(e)}")
            # في حالة عدم وجود جدول_المواد_والاقسام، استخدم جدول_البيانات كبديل
            try:
                conn = sqlite3.connect(self.db_path)
                cursor = conn.cursor()
                cursor.execute("SELECT DISTINCT القسم FROM جدول_البيانات WHERE القسم IS NOT NULL ORDER BY القسم")
                sections = [row[0] for row in cursor.fetchall()]
                combo.clear()
                combo.addItem("اختر القسم الجديد")
                combo.addItems(sections)
                conn.close()
            except Exception as backup_error:
                print(f"خطأ في تحميل الأقسام من الجدول البديل: {str(backup_error)}")

    def get_selected_students_info(self, selected_ids):
        """الحصول على معلومات التلاميذ المحددين"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            students_info = []
            for student_id in selected_ids:
                cursor.execute("""
                    SELECT اسم_التلميذ, رمز_التلميذ, القسم, اجمالي_مبلغ_التسجيل, الواجب_الشهري
                    FROM جدول_البيانات
                    WHERE id = ?
                """, (student_id,))

                result = cursor.fetchone()
                if result:
                    students_info.append({
                        'id': student_id,
                        'name': result[0] or 'غير محدد',
                        'code': result[1] or 'غير محدد',
                        'section': result[2] or 'غير محدد',
                        'registration_fee': result[3] or 0,
                        'monthly_duty': result[4] or 0
                    })

            conn.close()
            return students_info

        except Exception as e:
            print(f"خطأ في الحصول على معلومات التلاميذ: {str(e)}")
            return []

    def apply_bulk_changes(self, dialog, selected_ids, group_check, group_combo, section_check, section_combo,
                          registration_check, registration_input, monthly_check, monthly_input):
        """تطبيق التغييرات الجماعية"""
        try:
            # التحقق من وجود تغييرات للتطبيق
            has_changes = False
            changes_summary = []

            if group_check.isChecked() and group_combo.currentText() != "اختر المجموعة الجديدة":
                has_changes = True
                changes_summary.append(f"المجموعة الجديدة: {group_combo.currentText()}")

            if section_check.isChecked() and section_combo.currentText() != "اختر القسم الجديد":
                has_changes = True
                changes_summary.append(f"القسم الجديد: {section_combo.currentText()}")

            if registration_check.isChecked() and registration_input.text().strip():
                has_changes = True
                changes_summary.append(f"واجبات التسجيل: {registration_input.text().strip()}")

            if monthly_check.isChecked() and monthly_input.text().strip():
                has_changes = True
                changes_summary.append(f"الواجب الشهري: {monthly_input.text().strip()}")

            if not has_changes:
                QMessageBox.warning(
                    dialog,
                    "تحذير",
                    "لم يتم تحديد أي تغييرات للتطبيق.\nيرجى تحديد التغييرات المطلوبة."
                )
                return

            # تأكيد التطبيق
            confirmation_text = f"هل أنت متأكد من تطبيق التغييرات التالية على {len(selected_ids)} تلميذ؟\n\n"
            confirmation_text += "\n".join([f"• {change}" for change in changes_summary])

            reply = QMessageBox.question(
                dialog,
                "تأكيد التطبيق",
                confirmation_text,
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply != QMessageBox.Yes:
                return

            # تطبيق التغييرات
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            success_count = 0
            error_count = 0

            for student_id in selected_ids:
                try:
                    # بناء استعلام التحديث
                    update_fields = []
                    update_values = []

                    if group_check.isChecked() and group_combo.currentText() != "اختر المجموعة الجديدة":
                        update_fields.append("اسم_المجموعة = ?")
                        update_values.append(group_combo.currentText())

                    if section_check.isChecked() and section_combo.currentText() != "اختر القسم الجديد":
                        update_fields.append("القسم = ?")
                        update_values.append(section_combo.currentText())

                    if registration_check.isChecked() and registration_input.text().strip():
                        # حفظ النص كما هو (مع العملة)
                        registration_text = registration_input.text().strip()
                        update_fields.append("اجمالي_مبلغ_التسجيل = ?")
                        update_fields.append("مبلغ_القسط = ?")  # نفس المبلغ للقسط الواحد
                        update_values.append(registration_text)
                        update_values.append(registration_text)

                    if monthly_check.isChecked() and monthly_input.text().strip():
                        # حفظ النص كما هو (مع العملة)
                        monthly_text = monthly_input.text().strip()
                        update_fields.append("الواجب_الشهري = ?")
                        update_values.append(monthly_text)

                        # إعادة حساب المبلغ النهائي الشهري (محاولة استخراج الرقم)
                        cursor.execute("SELECT الاشهر_المحددة FROM جدول_البيانات WHERE id = ?", (student_id,))
                        months_result = cursor.fetchone()
                        if months_result and months_result[0]:
                            try:
                                import json
                                import re
                                selected_months = json.loads(months_result[0])

                                # محاولة استخراج الرقم من النص
                                numbers = re.findall(r'\d+\.?\d*', monthly_text)
                                if numbers:
                                    monthly_amount = float(numbers[0])
                                    total_monthly_amount = monthly_amount * len(selected_months)
                                    # حفظ المبلغ النهائي مع نفس العملة
                                    currency_part = re.sub(r'\d+\.?\d*', '', monthly_text).strip()
                                    total_monthly_text = f"{total_monthly_amount} {currency_part}".strip()
                                    update_fields.append("المبلغ_النهائي_الشهري = ?")
                                    update_values.append(total_monthly_text)
                            except:
                                # في حالة فشل التحليل، احفظ النص كما هو
                                update_fields.append("المبلغ_النهائي_الشهري = ?")
                                update_values.append(monthly_text)

                    # إضافة تاريخ التحديث
                    update_fields.append("تاريخ_التحديث = CURRENT_TIMESTAMP")
                    update_values.append(student_id)

                    # تنفيذ الاستعلام
                    query = f"UPDATE جدول_البيانات SET {', '.join(update_fields)} WHERE id = ?"
                    cursor.execute(query, update_values)

                    success_count += 1

                except Exception as e:
                    print(f"خطأ في تحديث التلميذ {student_id}: {str(e)}")
                    error_count += 1

            conn.commit()
            conn.close()

            # عرض نتائج التطبيق
            if success_count > 0:
                result_message = f"تم تطبيق التغييرات بنجاح على {success_count} تلميذ."
                if error_count > 0:
                    result_message += f"\nفشل في تحديث {error_count} تلميذ."

                QMessageBox.information(dialog, "نجح", result_message)

                # تحديث الجدول الرئيسي
                self.refresh_data()

                # إغلاق النافذة
                dialog.accept()
            else:
                QMessageBox.critical(
                    dialog,
                    "خطأ",
                    f"فشل في تطبيق التغييرات على جميع التلاميذ المحددين ({error_count} خطأ)."
                )

        except Exception as e:
            QMessageBox.critical(
                dialog,
                "خطأ",
                f"حدث خطأ أثناء تطبيق التغييرات: {str(e)}"
            )

    def handle_group_monthly_duties(self):
        """التعامل مع أداء الواجبات الشهرية لمجموعة التلاميذ المحددين"""
        try:
            selected_ids = self.get_selected_student_id()

            if len(selected_ids) == 0:
                QMessageBox.warning(
                    self,
                    "تحذير",
                    "يرجى تحديد تلميذ واحد أو أكثر لأداء الواجبات الشهرية."
                )
                return
            elif len(selected_ids) == 1:
                QMessageBox.information(
                    self,
                    "معلومة",
                    "تم تحديد تلميذ واحد فقط. يمكنك استخدام زر 'أداء الواجبات الشهرية' للتعامل مع تلميذ واحد.\n"
                    "أو تحديد المزيد من التلاميذ للأداء الجماعي."
                )
                # فتح نافذة الأداء للتلميذ الواحد
                self.open_single_monthly_duties_window(selected_ids[0])
            else:
                # أكثر من تلميذ محدد - فتح نافذة الأداء الجماعي
                self.open_group_monthly_duties_window(selected_ids)

        except Exception as e:
            QMessageBox.critical(
                self,
                "خطأ",
                f"حدث خطأ أثناء فتح نافذة أداء الواجبات الشهرية: {str(e)}"
            )

    def open_single_monthly_duties_window(self, student_id):
        """فتح نافذة أداء الواجبات الشهرية لتلميذ واحد"""
        try:
            # استيراد النافذة المطلوبة
            from sub242_window import MonthlyPaymentWindow

            # إنشاء وفتح النافذة
            self.monthly_payment_window = MonthlyPaymentWindow(
                parent=self,
                db_path=self.db_path,
                student_id=student_id
            )

            # إظهار النافذة
            self.monthly_payment_window.show()
            self.monthly_payment_window.raise_()
            self.monthly_payment_window.activateWindow()

        except ImportError:
            QMessageBox.critical(
                self,
                "خطأ",
                "لا يمكن العثور على نافذة أداء الواجبات الشهرية (sub242_window.py)."
            )
        except Exception as e:
            QMessageBox.critical(
                self,
                "خطأ",
                f"فشل في فتح نافذة أداء الواجبات الشهرية: {str(e)}"
            )

    def open_group_monthly_duties_window(self, selected_ids):
        """فتح نافذة أداء الواجبات الشهرية لمجموعة التلاميذ"""
        try:
            from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QPushButton,
                                       QLabel, QLineEdit, QComboBox, QCheckBox, QGroupBox,
                                       QGridLayout, QScrollArea, QWidget, QFrame, QDateEdit)
            from PyQt5.QtCore import QDate

            # إنشاء النافذة
            dialog = QDialog(self)
            dialog.setWindowTitle(f"أداء الواجبات الشهرية - {len(selected_ids)} تلميذ محدد")
            dialog.setModal(True)
            dialog.setLayoutDirection(Qt.RightToLeft)
            dialog.resize(800, 600)
            dialog.setStyleSheet("""
                QDialog {
                    background-color: #f8f9fa;
                }
                QGroupBox {
                    font-weight: bold;
                    font-size: 14px;
                    border: 2px solid #dee2e6;
                    border-radius: 8px;
                    margin-top: 10px;
                    padding-top: 10px;
                }
                QGroupBox::title {
                    subcontrol-origin: margin;
                    left: 10px;
                    padding: 0 5px 0 5px;
                }
                QFrame {
                    border: 1px solid #dee2e6;
                    border-radius: 5px;
                    background-color: white;
                    margin: 2px;
                }
            """)

            layout = QVBoxLayout(dialog)

            # عنوان النافذة
            title_label = QLabel(f"📋 أداء الواجبات الشهرية لـ {len(selected_ids)} تلميذ")
            title_label.setFont(QFont("Calibri", 16, QFont.Bold))
            title_label.setStyleSheet("color: #2c3e50; margin: 10px; text-align: center;")
            layout.addWidget(title_label)

            # مجموعة معلومات الدفع
            payment_group = QGroupBox("💰 معلومات الدفع")
            payment_layout = QGridLayout(payment_group)

            # المبلغ المدفوع
            payment_layout.addWidget(QLabel("المبلغ المدفوع:"), 0, 0)
            amount_input = QLineEdit()
            amount_input.setFont(QFont("Calibri", 13, QFont.Bold))
            amount_input.setPlaceholderText("مثال: 200 درهم")
            amount_input.setStyleSheet("""
                QLineEdit {
                    padding: 8px;
                    border: 2px solid #ced4da;
                    border-radius: 5px;
                    background-color: white;
                }
                QLineEdit:focus {
                    border: 2px solid #007bff;
                }
            """)
            payment_layout.addWidget(amount_input, 0, 1)

            # تاريخ الدفع
            payment_layout.addWidget(QLabel("تاريخ الدفع:"), 1, 0)
            date_input = QDateEdit()
            date_input.setDate(QDate.currentDate())
            date_input.setFont(QFont("Calibri", 13, QFont.Bold))
            date_input.setStyleSheet("""
                QDateEdit {
                    padding: 8px;
                    border: 2px solid #ced4da;
                    border-radius: 5px;
                    background-color: white;
                }
            """)
            payment_layout.addWidget(date_input, 1, 1)

            # حالة الدفع
            payment_layout.addWidget(QLabel("حالة الدفع:"), 2, 0)
            status_combo = QComboBox()
            status_combo.addItems(["مدفوع كاملاً", "مدفوع جزئياً", "غير مدفوع"])
            status_combo.setFont(QFont("Calibri", 13, QFont.Bold))
            status_combo.setStyleSheet("""
                QComboBox {
                    padding: 8px;
                    border: 2px solid #ced4da;
                    border-radius: 5px;
                    background-color: white;
                }
            """)
            payment_layout.addWidget(status_combo, 2, 1)

            # الشهر المحدد
            payment_layout.addWidget(QLabel("الشهر المحدد:"), 3, 0)
            month_combo = QComboBox()
            month_combo.addItems([
                "يناير", "فبراير", "مارس", "أبريل", "مايو", "يونيو",
                "يوليو", "أغسطس", "سبتمبر", "أكتوبر", "نوفمبر", "ديسمبر"
            ])
            # تعيين الشهر الحالي كافتراضي
            from datetime import datetime
            current_month = datetime.now().month - 1  # فهرس الشهر (0-11)
            month_combo.setCurrentIndex(current_month)
            month_combo.setFont(QFont("Calibri", 13, QFont.Bold))
            month_combo.setStyleSheet("""
                QComboBox {
                    padding: 8px;
                    border: 2px solid #ced4da;
                    border-radius: 5px;
                    background-color: white;
                }
            """)
            payment_layout.addWidget(month_combo, 3, 1)

            # ملاحظات إضافية
            payment_layout.addWidget(QLabel("ملاحظات إضافية:"), 4, 0)
            notes_input = QLineEdit()
            notes_input.setFont(QFont("Calibri", 13, QFont.Bold))
            notes_input.setPlaceholderText("ملاحظات اختيارية...")
            notes_input.setStyleSheet("""
                QLineEdit {
                    padding: 8px;
                    border: 2px solid #ced4da;
                    border-radius: 5px;
                    background-color: white;
                }
            """)
            payment_layout.addWidget(notes_input, 4, 1)

            layout.addWidget(payment_group)

            # مجموعة التلاميذ المحددين
            students_group = QGroupBox("👥 التلاميذ المحددين")
            students_layout = QVBoxLayout(students_group)

            # منطقة التمرير للتلاميذ
            scroll_area = QScrollArea()
            scroll_widget = QWidget()
            scroll_layout = QVBoxLayout(scroll_widget)

            students_info = self.get_selected_students_info(selected_ids)

            for info in students_info:
                student_frame = QFrame()
                student_layout = QHBoxLayout(student_frame)

                # معلومات التلميذ
                student_info_label = QLabel(f"• {info['name']} (رمز: {info['code']}) - القسم: {info['section']}")
                student_info_label.setFont(QFont("Calibri", 13, QFont.Bold))
                student_layout.addWidget(student_info_label)

                # مربع اختيار للتطبيق
                apply_checkbox = QCheckBox("تطبيق")
                apply_checkbox.setChecked(True)
                apply_checkbox.setFont(QFont("Calibri", 13, QFont.Bold))
                student_layout.addWidget(apply_checkbox)

                # حفظ معرف التلميذ مع مربع الاختيار
                apply_checkbox.student_id = info['id']

                scroll_layout.addWidget(student_frame)

            scroll_area.setWidget(scroll_widget)
            scroll_area.setWidgetResizable(True)
            scroll_area.setMaximumHeight(200)
            students_layout.addWidget(scroll_area)

            layout.addWidget(students_group)

            # الأزرار
            button_layout = QHBoxLayout()

            # زر التطبيق
            apply_btn = QPushButton("✅ تسجيل الدفع")
            apply_btn.setFont(QFont("Calibri", 13, QFont.Bold))
            apply_btn.setStyleSheet("""
                QPushButton {
                    background-color: #28a745;
                    color: white;
                    border: none;
                    border-radius: 8px;
                    padding: 12px 20px;
                    margin: 5px;
                }
                QPushButton:hover {
                    background-color: #218838;
                }
            """)
            apply_btn.clicked.connect(lambda: self.apply_group_payment(
                dialog, amount_input, date_input, status_combo, month_combo, notes_input, scroll_widget
            ))

            # زر الإلغاء
            cancel_btn = QPushButton("❌ إلغاء")
            cancel_btn.setFont(QFont("Calibri", 13, QFont.Bold))
            cancel_btn.setStyleSheet("""
                QPushButton {
                    background-color: #dc3545;
                    color: white;
                    border: none;
                    border-radius: 8px;
                    padding: 12px 20px;
                    margin: 5px;
                }
                QPushButton:hover {
                    background-color: #c82333;
                }
            """)
            cancel_btn.clicked.connect(dialog.reject)

            button_layout.addWidget(apply_btn)
            button_layout.addWidget(cancel_btn)

            layout.addLayout(button_layout)

            # عرض النافذة
            dialog.exec_()

        except Exception as e:
            QMessageBox.critical(
                self,
                "خطأ",
                f"فشل في فتح نافذة أداء الواجبات الشهرية الجماعية: {str(e)}"
            )

    def apply_group_payment(self, dialog, amount_input, date_input, status_combo, month_combo, notes_input, scroll_widget):
        """تطبيق الدفع الجماعي للواجبات الشهرية"""
        try:
            from PyQt5.QtWidgets import QCheckBox

            # التحقق من البيانات المطلوبة
            if not amount_input.text().strip():
                QMessageBox.warning(
                    dialog,
                    "تحذير",
                    "يرجى إدخال المبلغ المدفوع."
                )
                return

            # جمع التلاميذ المحددين للتطبيق
            selected_students = []
            for child in scroll_widget.findChildren(QCheckBox):
                if hasattr(child, 'student_id') and child.isChecked():
                    selected_students.append(child.student_id)

            if not selected_students:
                QMessageBox.warning(
                    dialog,
                    "تحذير",
                    "يرجى تحديد تلميذ واحد على الأقل لتطبيق الدفع."
                )
                return

            # تأكيد العملية
            confirmation_text = f"هل أنت متأكد من تسجيل الدفع للتلاميذ المحددين؟\n\n"
            confirmation_text += f"المبلغ: {amount_input.text().strip()}\n"
            confirmation_text += f"الشهر: {month_combo.currentText()}\n"
            confirmation_text += f"التاريخ: {date_input.date().toString('yyyy-MM-dd')}\n"
            confirmation_text += f"الحالة: {status_combo.currentText()}\n"
            confirmation_text += f"عدد التلاميذ: {len(selected_students)}"

            reply = QMessageBox.question(
                dialog,
                "تأكيد تسجيل الدفع",
                confirmation_text,
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply != QMessageBox.Yes:
                return

            # تطبيق الدفع
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # إنشاء جدول الواجبات الشهرية إذا لم يكن موجوداً (نفس النظام المستخدم في monthly_duties_window.py)
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS monthly_duties (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    student_id INTEGER NOT NULL,
                    month TEXT NOT NULL,
                    year INTEGER NOT NULL,
                    amount_required REAL NOT NULL DEFAULT 0,
                    amount_paid REAL NOT NULL DEFAULT 0,
                    amount_remaining REAL NOT NULL DEFAULT 0,
                    payment_date TEXT,
                    payment_status TEXT NOT NULL DEFAULT 'غير مدفوع',
                    notes TEXT,
                    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (student_id) REFERENCES جدول_البيانات(id)
                )
            """)

            # إنشاء جدول الأداءات القديم للتوافق مع النظام السابق
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS جدول_الاداءات (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    رمز_التلميذ TEXT,
                    اسم_التلميذ TEXT,
                    القسم TEXT,
                    الشهر_المحدد TEXT,
                    المبلغ_المدفوع TEXT,
                    تاريخ_الدفع DATE,
                    حالة_الدفع TEXT,
                    ملاحظات TEXT,
                    تاريخ_التسجيل DATETIME DEFAULT CURRENT_TIMESTAMP,
                    معرف_التلميذ INTEGER,
                    FOREIGN KEY (معرف_التلميذ) REFERENCES جدول_البيانات (id)
                )
            """)

            success_count = 0
            error_count = 0

            # أولاً، دعنا نتحقق من أسماء الأعمدة المتاحة
            cursor.execute("PRAGMA table_info(جدول_البيانات)")
            columns_info = cursor.fetchall()
            print("أعمدة جدول_البيانات المتاحة:")
            for col in columns_info:
                print(f"  - {col[1]} ({col[2]})")

            for student_id in selected_students:
                try:
                    # جلب معلومات التلميذ مع محاولة أسماء مختلفة للواجب الشهري
                    cursor.execute("""
                        SELECT اسم_التلميذ, رمز_التلميذ, القسم,
                               COALESCE(الواجب_الشهري, المبلغ_النهائي_الشهري, 0) as monthly_amount
                        FROM جدول_البيانات
                        WHERE id = ?
                    """, (student_id,))

                    student_data = cursor.fetchone()
                    if student_data:
                        # طباعة البيانات المسترجعة للتشخيص
                        print(f"البيانات المسترجعة: {student_data}")
                        print(f"عدد الأعمدة: {len(student_data)}")

                        # التحقق من البيانات قبل الإدراج
                        student_code = student_data[1] if len(student_data) > 1 and student_data[1] else f"ID_{student_id}"
                        student_name = student_data[0] if len(student_data) > 0 and student_data[0] else "غير محدد"
                        student_section = student_data[2] if len(student_data) > 2 and student_data[2] else "غير محدد"

                        print(f"إدراج دفع للتلميذ: {student_name} (رمز: {student_code})")

                        # حساب البيانات المالية (نفس النظام المستخدم في monthly_duties_window.py)
                        try:
                            amount_paid = float(amount_input.text().strip().replace('درهم', '').replace(',', '').strip())
                        except:
                            amount_paid = 0.0

                        # الحصول على الواجب الشهري للتلميذ من بيانات التلميذ (الفهرس 3 هو monthly_amount)
                        if len(student_data) > 3 and student_data[3]:
                            monthly_duty = float(student_data[3])
                        else:
                            # إذا لم يكن عمود الواجب الشهري موجود، استخدم المبلغ المدفوع كافتراضي
                            monthly_duty = amount_paid
                            print(f"تحذير: لم يتم العثور على الواجب الشهري، استخدام المبلغ المدفوع: {amount_paid}")
                        amount_required = float(monthly_duty) if monthly_duty else amount_paid
                        amount_remaining = max(0, amount_required - amount_paid)

                        # تحديد حالة الدفع
                        if amount_paid == 0:
                            payment_status = "غير مدفوع"
                        elif amount_paid >= amount_required:
                            payment_status = "مدفوع كاملاً"
                            amount_remaining = 0
                        else:
                            payment_status = "مدفوع جزئياً"

                        # الحصول على السنة من الشهر المحدد
                        current_year = datetime.now().year

                        # إدراج في جدول monthly_duties (النظام الجديد)
                        cursor.execute("""
                            INSERT INTO monthly_duties
                            (student_id, month, year, amount_required, amount_paid, amount_remaining,
                             payment_date, payment_status, notes)
                            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                        """, (
                            student_id,  # معرف التلميذ
                            month_combo.currentText(),  # الشهر المحدد
                            current_year,  # السنة الحالية
                            amount_required,  # المبلغ المطلوب
                            amount_paid,  # المبلغ المدفوع
                            amount_remaining,  # المبلغ المتبقي
                            date_input.date().toString('yyyy-MM-dd'),  # تاريخ الدفع
                            payment_status,  # حالة الدفع
                            notes_input.text().strip()  # الملاحظات
                        ))

                        # إدراج في جدول_الاداءات أيضاً للتوافق مع النظام السابق
                        cursor.execute("""
                            INSERT INTO جدول_الاداءات
                            (رمز_التلميذ, اسم_التلميذ, القسم, الشهر_المحدد, المبلغ_المدفوع,
                             تاريخ_الدفع, حالة_الدفع, ملاحظات, معرف_التلميذ)
                            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                        """, (
                            student_code,  # رمز التلميذ
                            student_name,  # اسم التلميذ
                            student_section,  # القسم
                            month_combo.currentText(),  # الشهر المحدد
                            f"{amount_paid:.2f} درهم",  # المبلغ المدفوع
                            date_input.date().toString('yyyy-MM-dd'),  # تاريخ الدفع
                            payment_status,  # حالة الدفع
                            notes_input.text().strip(),  # الملاحظات
                            student_id  # معرف التلميذ
                        ))

                        success_count += 1
                        print(f"✅ تم تسجيل الدفع بنجاح للتلميذ {student_name}")
                        print(f"   المبلغ المطلوب: {amount_required:.2f} درهم")
                        print(f"   المبلغ المدفوع: {amount_paid:.2f} درهم")
                        print(f"   المبلغ المتبقي: {amount_remaining:.2f} درهم")
                        print(f"   حالة الدفع: {payment_status}")
                    else:
                        print(f"❌ لم يتم العثور على بيانات التلميذ {student_id}")
                        error_count += 1

                except Exception as e:
                    print(f"خطأ في تسجيل دفع التلميذ {student_id}: {str(e)}")
                    import traceback
                    traceback.print_exc()
                    error_count += 1

            conn.commit()
            conn.close()

            # عرض نتائج العملية
            if success_count > 0:
                result_message = f"تم تسجيل الدفع بنجاح لـ {success_count} تلميذ."
                if error_count > 0:
                    result_message += f"\nفشل في تسجيل الدفع لـ {error_count} تلميذ."

                QMessageBox.information(dialog, "نجح", result_message)

                # إغلاق النافذة
                dialog.accept()
            else:
                QMessageBox.critical(
                    dialog,
                    "خطأ",
                    f"فشل في تسجيل الدفع لجميع التلاميذ المحددين ({error_count} خطأ)."
                )

        except Exception as e:
            QMessageBox.critical(
                dialog,
                "خطأ",
                f"حدث خطأ أثناء تسجيل الدفع: {str(e)}"
            )

# تشغيل التطبيق للاختبار
if __name__ == "__main__":
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)
    
    window = DataViewWindow()
    window.show()
    
    sys.exit(app.exec_())
