#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
import sqlite3
import calendar
import subprocess
from datetime import datetime, timedelta

try:
    from fpdf import FPDF
    import arabic_reshaper
    from bidi.algorithm import get_display
except ImportError:
    subprocess.check_call([sys.executable, "-m", "pip", "install", "fpdf2", "arabic-reshaper", "python-bidi"])
    from fpdf import FPDF
    import arabic_reshaper
    from bidi.algorithm import get_display

class ArabicPDF(FPDF):
    def __init__(self):
        super().__init__('L', 'mm', 'A4')  # أفقي للجدول العريض
        self.set_margins(10, 10, 10)
        self.set_auto_page_break(auto=True, margin=10)
        
        # إضافة الخطوط
        fonts_dir = os.path.join(os.path.dirname(__file__), 'fonts')
        calibri_path = os.path.join(fonts_dir, 'calibri.ttf')
        calibri_bold_path = os.path.join(fonts_dir, 'calibrib.ttf')
        
        if os.path.exists(calibri_path):
            self.add_font('Calibri', '', calibri_path)
            self.calibri_available = True
        else:
            self.calibri_available = False
            
        if os.path.exists(calibri_bold_path):
            self.add_font('Calibri', 'B', calibri_bold_path)
            self.calibri_bold_available = True
        else:
            self.calibri_bold_available = False
        
        # تعيين الخط الافتراضي
        if self.calibri_available:
            self.set_font('Calibri', '', 10)
        else:
            self.set_font('Arial', '', 10)

    def set_title_font(self, size=15):
        """خط العناوين الرئيسية"""
        if self.calibri_bold_available:
            self.set_font('Calibri', 'B', size)
        else:
            self.set_font('Arial', 'B', size)

    def set_subtitle_font(self, size=14):
        """خط العناوين الفرعية"""
        if self.calibri_bold_available:
            self.set_font('Calibri', 'B', size)
        else:
            self.set_font('Arial', 'B', size)

    def set_detail_font(self, size=13):
        """خط التفاصيل"""
        if self.calibri_bold_available:
            self.set_font('Calibri', 'B', size)
        else:
            self.set_font('Arial', 'B', size)

    def ar_text(self, txt: str) -> str:
        """تحويل النص العربي"""
        reshaped = arabic_reshaper.reshape(str(txt))
        return get_display(reshaped)

def get_weeks_in_month(year, month):
    """حساب الأسابيع في الشهر (الاثنين بداية الأسبوع)"""
    first_day = datetime(year, month, 1)
    last_day = datetime(year, month, calendar.monthrange(year, month)[1])
    
    # العثور على أول اثنين
    days_until_monday = (7 - first_day.weekday()) % 7
    if first_day.weekday() != 0:
        first_monday = first_day + timedelta(days=days_until_monday)
    else:
        first_monday = first_day
    
    weeks = []
    current_monday = first_monday
    
    while current_monday <= last_day:
        week_end = current_monday + timedelta(days=6)
        if week_end > last_day:
            week_end = last_day
        
        weeks.append({
            'start': current_monday,
            'end': week_end,
            'week_num': len(weeks) + 1
        })
        
        current_monday += timedelta(days=7)
    
    return weeks

def generate_attendance_sheet_pdf(section, year, month, month_name, db_path="data.db"):
    """إنشاء ورقة متابعة الغياب PDF"""
    try:
        print(f"🔧 بدء إنشاء ورقة PDF للقسم: {section}")
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # جلب معلومات القسم
        try:
            cursor.execute("""
                SELECT القسم, المادة, اسم_الاستاذ
                FROM جدول_المواد_والاقسام
                WHERE القسم = ?
                LIMIT 1
            """, (section,))

            section_info = cursor.fetchone()
            if not section_info:
                section_info = (section, "غير محدد", "غير محدد")
        except Exception as e:
            print(f"⚠️ خطأ في جلب معلومات القسم: {e}")
            section_info = (section, "غير محدد", "غير محدد")
        
        # جلب التلاميذ مرتبين حسب الرمز
        cursor.execute("""
            SELECT id, اسم_التلميذ, رمز_التلميذ
            FROM جدول_البيانات 
            WHERE القسم = ? AND اسم_التلميذ IS NOT NULL AND اسم_التلميذ != ''
            ORDER BY CAST(رمز_التلميذ AS INTEGER) ASC, اسم_التلميذ ASC
        """, (section,))
        
        students = cursor.fetchall()
        conn.close()
        
        if not students:
            raise Exception(f"لا يوجد تلاميذ في القسم '{section}'")
        
        # حساب الأسابيع
        weeks = get_weeks_in_month(year, month)
        
        # إنشاء PDF
        pdf = ArabicPDF()
        pdf.add_page()
        
        # الشعار والعنوان
        y = 15
        
        # شعار المؤسسة (إذا كان متوفراً)
        logo_path = None
        try:
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            cursor.execute("SELECT ImagePath1 FROM بيانات_المؤسسة LIMIT 1")
            logo_row = cursor.fetchone()
            if logo_row and os.path.exists(logo_row[0]):
                logo_path = logo_row[0]
            conn.close()
        except:
            pass
        
        if logo_path:
            pdf.image(logo_path, x=120, y=y, w=40, h=20)
            y += 25
        else:
            # شعار نصي
            pdf.set_title_font(18)
            pdf.set_xy(10, y)
            pdf.cell(277, 10, pdf.ar_text("🏫 مؤسسة التعليم"), border=0, align='C')
            y += 15
        
        # عنوان الورقة
        pdf.set_title_font(16)
        pdf.set_xy(10, y)
        pdf.cell(277, 10, pdf.ar_text("ورقة متابعة الغياب الشهرية"), border=0, align='C')
        y += 10
        
        pdf.set_subtitle_font(14)
        pdf.set_xy(10, y)
        pdf.cell(277, 8, pdf.ar_text(f"شهر {month_name} {year}"), border=0, align='C')
        y += 15
        
        # معلومات القسم
        pdf.set_detail_font(12)
        info_y = y
        
        # الصف الأول من المعلومات
        pdf.set_xy(10, info_y)
        pdf.cell(60, 8, pdf.ar_text(f"القسم: {section_info[0]}"), border=1, align='C')
        pdf.set_xy(70, info_y)
        pdf.cell(60, 8, pdf.ar_text(f"المادة: {section_info[1]}"), border=1, align='C')
        pdf.set_xy(130, info_y)
        pdf.cell(60, 8, pdf.ar_text(f"الأستاذ(ة): {section_info[2]}"), border=1, align='C')
        pdf.set_xy(190, info_y)
        pdf.cell(60, 8, pdf.ar_text(f"عدد التلاميذ: {len(students)}"), border=1, align='C')
        
        y += 15
        
        # جدول الغياب
        pdf.set_detail_font(9)
        
        # حساب عرض الأعمدة
        col_widths = {
            'order': 15,      # رقم الترتيب
            'name': 50,       # الاسم
            'id': 20,         # ID
            'session': 12     # كل حصة
        }
        
        total_sessions = len(weeks) * 3
        table_width = col_widths['order'] + col_widths['name'] + col_widths['id'] + (total_sessions * col_widths['session'])
        
        # رأس الجدول - الصف الأول
        x = 10
        pdf.set_xy(x, y)
        pdf.cell(col_widths['order'], 16, pdf.ar_text("رقم الترتيب"), border=1, align='C')
        x += col_widths['order']
        
        pdf.set_xy(x, y)
        pdf.cell(col_widths['name'], 16, pdf.ar_text("الاسم والنسب"), border=1, align='C')
        x += col_widths['name']
        
        pdf.set_xy(x, y)
        pdf.cell(col_widths['id'], 16, pdf.ar_text("ID"), border=1, align='C')
        x += col_widths['id']
        
        # رؤوس الأسابيع
        for week in weeks:
            pdf.set_xy(x, y)
            pdf.cell(col_widths['session'] * 3, 8, pdf.ar_text(f"الأسبوع {week['week_num']}"), border=1, align='C')
            x += col_widths['session'] * 3
        
        # رأس الجدول - الصف الثاني (الحصص)
        y += 8
        x = 10 + col_widths['order'] + col_widths['name'] + col_widths['id']
        
        for week in weeks:
            pdf.set_xy(x, y)
            pdf.cell(col_widths['session'], 8, pdf.ar_text("ح1"), border=1, align='C')
            x += col_widths['session']
            
            pdf.set_xy(x, y)
            pdf.cell(col_widths['session'], 8, pdf.ar_text("ح2"), border=1, align='C')
            x += col_widths['session']
            
            pdf.set_xy(x, y)
            pdf.cell(col_widths['session'], 8, pdf.ar_text("ح3"), border=1, align='C')
            x += col_widths['session']
        
        y += 8
        
        # صفوف التلاميذ
        pdf.set_detail_font(8)
        for i, student in enumerate(students, 1):
            student_id, student_name, student_code = student
            
            x = 10
            
            # رقم الترتيب
            pdf.set_xy(x, y)
            pdf.cell(col_widths['order'], 8, str(i), border=1, align='C')
            x += col_widths['order']
            
            # اسم التلميذ
            pdf.set_xy(x, y)
            pdf.cell(col_widths['name'], 8, pdf.ar_text(str(student_name)), border=1, align='R')
            x += col_widths['name']
            
            # ID
            pdf.set_xy(x, y)
            pdf.cell(col_widths['id'], 8, str(student_code or student_id), border=1, align='C')
            x += col_widths['id']
            
            # خلايا الحصص (فارغة للتعبئة)
            for week in weeks:
                for session in range(3):
                    pdf.set_xy(x, y)
                    pdf.cell(col_widths['session'], 8, "", border=1, align='C')
                    x += col_widths['session']
            
            y += 8
            
            # انتقال لصفحة جديدة عند الحاجة
            if y > 180:
                pdf.add_page()
                y = 20
        
        # ملاحظات
        y += 5
        pdf.set_detail_font(10)
        pdf.set_xy(10, y)
        pdf.cell(277, 6, pdf.ar_text("ملاحظات:"), border=0, align='R')
        y += 8
        
        notes = [
            "• يتم وضع علامة (×) في الخانة المقابلة للحصة التي غاب فيها التلميذ",
            "• ح1: الحصة الأولى، ح2: الحصة الثانية، ح3: الحصة الثالثة",
            "• يبدأ الأسبوع من يوم الاثنين وينتهي يوم الأحد"
        ]
        
        for note in notes:
            pdf.set_xy(10, y)
            pdf.cell(277, 5, pdf.ar_text(note), border=0, align='R')
            y += 6
        
        # التوقيع والتاريخ
        y += 10
        pdf.set_xy(10, y)
        pdf.cell(100, 8, pdf.ar_text(f"التاريخ: {datetime.now().strftime('%Y-%m-%d')}"), border=0, align='R')
        
        pdf.set_xy(180, y)
        pdf.cell(80, 8, pdf.ar_text("التوقيع: ________________"), border=0, align='R')
        
        return pdf
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء PDF: {e}")
        raise

def create_attendance_sheet_report(section, year, month, month_name, db_path="data.db"):
    """إنشاء تقرير ورقة متابعة الغياب وفتحه"""
    try:
        # إنشاء PDF
        pdf = generate_attendance_sheet_pdf(section, year, month, month_name, db_path)
        
        # إنشاء مجلد التقارير
        reports_dir = os.path.join(os.path.expanduser('~'), 'Desktop', 'تقارير متابعة الغياب')
        if not os.path.exists(reports_dir):
            os.makedirs(reports_dir)
        
        # تحديد اسم الملف (إزالة الأحرف الخاصة)
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        safe_section = section.replace("/", "_").replace("\\", "_").replace(" ", "_")
        filename = f"ورقة_متابعة_الغياب_{safe_section}_{month_name}_{year}_{timestamp}.pdf"
        output_path = os.path.join(reports_dir, filename)
        
        # حفظ الملف
        pdf.output(output_path)
        print(f"✅ تم إنشاء التقرير: {output_path}")
        
        # فتح الملف
        try:
            if sys.platform == 'win32':
                os.startfile(output_path)
            elif sys.platform == 'darwin':  # macOS
                subprocess.call(['open', output_path])
            else:  # Linux
                subprocess.call(['xdg-open', output_path])
        except Exception as e:
            print(f"⚠️ تم إنشاء التقرير ولكن تعذر فتحه: {e}")
        
        return True, output_path, "تم إنشاء ورقة متابعة الغياب بنجاح"
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء التقرير: {e}")
        return False, None, f"خطأ في إنشاء التقرير: {str(e)}"

if __name__ == "__main__":
    # اختبار
    success, path, message = create_attendance_sheet_report("الأولى ابتدائي", 2024, 1, "يناير")
    print(f"النتيجة: {success}")
    print(f"المسار: {path}")
    print(f"الرسالة: {message}")
