#!/usr/bin/env python
# -*- coding: utf-8 -*-

import sys
import os
import sqlite3
from datetime import datetime

def test_section_monthly_report():
    """اختبار وظيفة تقرير القسم الشهري"""
    
    print("🔍 اختبار وظيفة تقرير القسم الشهري...")
    
    try:
        # التحقق من وجود قاعدة البيانات
        if not os.path.exists('data.db'):
            print("❌ ملف قاعدة البيانات غير موجود")
            return False
        
        # فحص الجداول المطلوبة
        conn = sqlite3.connect('data.db')
        cursor = conn.cursor()
        
        # فحص جدول جدول_البيانات
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='جدول_البيانات'")
        if not cursor.fetchone():
            print("❌ جدول جدول_البيانات غير موجود")
            conn.close()
            return False
        
        print("✅ جدول جدول_البيانات موجود")
        
        # فحص جدول جدول_المواد_والاقسام
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='جدول_المواد_والاقسام'")
        if cursor.fetchone():
            print("✅ جدول جدول_المواد_والاقسام موجود")
            cursor.execute("SELECT COUNT(*) FROM جدول_المواد_والاقسام")
            subjects_count = cursor.fetchone()[0]
            print(f"📚 عدد المواد والأقسام: {subjects_count}")
        else:
            print("⚠️ جدول جدول_المواد_والاقسام غير موجود")
        
        # فحص جدول monthly_duties
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='monthly_duties'")
        if cursor.fetchone():
            print("✅ جدول monthly_duties موجود")
            cursor.execute("SELECT COUNT(*) FROM monthly_duties")
            duties_count = cursor.fetchone()[0]
            print(f"💰 عدد الأداءات الشهرية: {duties_count}")
        else:
            print("⚠️ جدول monthly_duties غير موجود")
        
        # جلب الأقسام المتوفرة
        cursor.execute("SELECT DISTINCT القسم FROM جدول_البيانات WHERE القسم IS NOT NULL AND القسم != ''")
        sections = cursor.fetchall()
        
        if not sections:
            print("❌ لا توجد أقسام في قاعدة البيانات")
            conn.close()
            return False
        
        print(f"📋 الأقسام المتوفرة: {len(sections)}")
        for section in sections[:5]:  # عرض أول 5 أقسام
            print(f"   - {section[0]}")
        
        # اختيار أول قسم للاختبار
        test_section = sections[0][0]
        test_month = "يناير"
        
        print(f"🎯 اختبار التقرير للقسم: {test_section}")
        print(f"📅 الشهر: {test_month}")
        
        # فحص عدد التلاميذ في القسم
        cursor.execute("SELECT COUNT(*) FROM جدول_البيانات WHERE القسم = ?", (test_section,))
        students_in_section = cursor.fetchone()[0]
        print(f"👥 عدد التلاميذ في القسم: {students_in_section}")
        
        conn.close()
        
        # اختبار استيراد وحدة print_section_monthly
        try:
            from print_section_monthly import print_section_monthly_report
            print("✅ تم استيراد وحدة print_section_monthly بنجاح")
        except ImportError as e:
            print(f"❌ فشل في استيراد وحدة print_section_monthly: {e}")
            return False
        
        # اختبار إنشاء التقرير
        print(f"🚀 بدء إنشاء تقرير القسم الشهري...")
        
        success, output_path, message = print_section_monthly_report(
            parent=None, 
            section=test_section,
            month=test_month
        )
        
        if success:
            print(f"✅ تم إنشاء التقرير بنجاح!")
            print(f"📁 مسار الملف: {output_path}")
            print(f"💬 الرسالة: {message}")
            
            # التحقق من وجود الملف
            if os.path.exists(output_path):
                file_size = os.path.getsize(output_path)
                print(f"📏 حجم الملف: {file_size} بايت")
                return True
            else:
                print("❌ الملف غير موجود رغم نجاح العملية")
                return False
        else:
            print(f"❌ فشل في إنشاء التقرير: {message}")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def create_sample_data():
    """إنشاء بيانات تجريبية للاختبار"""
    try:
        conn = sqlite3.connect('data.db')
        cursor = conn.cursor()
        
        # إنشاء جدول monthly_duties إذا لم يكن موجوداً
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS monthly_duties (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                student_id INTEGER,
                month TEXT,
                amount_required REAL DEFAULT 0,
                amount_paid REAL DEFAULT 0,
                amount_remaining REAL DEFAULT 0,
                payment_status TEXT DEFAULT 'غير مدفوع',
                payment_date TEXT,
                notes TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (student_id) REFERENCES جدول_البيانات(id)
            )
        """)
        
        # إنشاء جدول جدول_المواد_والاقسام إذا لم يكن موجوداً
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS جدول_المواد_والاقسام (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                القسم TEXT,
                المادة TEXT,
                اسم_الاستاذ TEXT,
                المجموعة TEXT,
                نسبة_الواجبات REAL DEFAULT 100
            )
        """)
        
        # إضافة بيانات تجريبية للمواد والأقسام
        sample_subjects = [
            ('قسم / 01', 'الرياضيات', 'أحمد محمد', 'المجموعة الأولى', 100),
            ('قسم / 01', 'العلوم', 'فاطمة علي', 'المجموعة الأولى', 90),
            ('قسم / 01', 'اللغة العربية', 'محمد حسن', 'المجموعة الأولى', 95),
            ('قسم / 02', 'الرياضيات', 'سارة أحمد', 'المجموعة الثانية', 100),
            ('قسم / 02', 'الفيزياء', 'عمر خالد', 'المجموعة الثانية', 85),
        ]
        
        for subject in sample_subjects:
            cursor.execute("""
                INSERT OR IGNORE INTO جدول_المواد_والاقسام 
                (القسم, المادة, اسم_الاستاذ, المجموعة, نسبة_الواجبات)
                VALUES (?, ?, ?, ?, ?)
            """, subject)
        
        # إضافة بيانات تجريبية للأداءات الشهرية
        cursor.execute("SELECT id, القسم FROM جدول_البيانات LIMIT 10")
        students = cursor.fetchall()
        
        months = ['يناير', 'فبراير', 'مارس', 'أبريل']
        
        for student in students:
            student_id, section = student
            for month in months:
                # التحقق من عدم وجود السجل مسبقاً
                cursor.execute("""
                    SELECT COUNT(*) FROM monthly_duties 
                    WHERE student_id = ? AND month = ?
                """, (student_id, month))
                
                if cursor.fetchone()[0] == 0:
                    amount_required = 500.0
                    amount_paid = 300.0 if month == 'يناير' else 500.0
                    amount_remaining = amount_required - amount_paid
                    
                    if amount_remaining <= 0:
                        payment_status = 'مدفوع كاملاً'
                    elif amount_paid > 0:
                        payment_status = 'مدفوع جزئياً'
                    else:
                        payment_status = 'غير مدفوع'
                    
                    cursor.execute("""
                        INSERT INTO monthly_duties 
                        (student_id, month, amount_required, amount_paid, amount_remaining, 
                         payment_status, payment_date, notes)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                    """, (
                        student_id, month, amount_required, amount_paid, amount_remaining,
                        payment_status, '2024-01-15', f'أداء شهر {month}'
                    ))
        
        conn.commit()
        conn.close()
        print("✅ تم إنشاء البيانات التجريبية بنجاح")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء البيانات التجريبية: {e}")
        return False

if __name__ == "__main__":
    print("=" * 60)
    print("🧪 اختبار وظيفة تقرير القسم الشهري")
    print("=" * 60)
    
    # إنشاء بيانات تجريبية أولاً
    print("📝 إنشاء البيانات التجريبية...")
    create_sample_data()
    
    print("\n" + "=" * 60)
    
    # تشغيل الاختبار
    result = test_section_monthly_report()
    
    print("=" * 60)
    if result:
        print("🎉 الاختبار نجح! تقرير القسم الشهري يعمل بشكل صحيح")
    else:
        print("💥 الاختبار فشل! يحتاج تقرير القسم الشهري إلى إصلاح")
    print("=" * 60)
