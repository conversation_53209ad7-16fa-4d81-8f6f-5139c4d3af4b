#!/usr/bin/env python
# -*- coding: utf-8 -*-

import sqlite3
import os

def test_groups_table_exclusive():
    """اختبار استخدام جدول المجموعات حصرياً"""
    print("🧪 اختبار استخدام جدول المجموعات حصرياً")
    print("=" * 50)
    
    if not os.path.exists('data.db'):
        print("❌ ملف قاعدة البيانات غير موجود")
        return False
    
    try:
        conn = sqlite3.connect('data.db')
        cursor = conn.cursor()
        
        # فحص وجود جدول المجموعات
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='جدول_المجموعات'")
        table_exists = cursor.fetchone()
        
        print("📋 نتائج الفحص:")
        
        if table_exists:
            print("✅ جدول المجموعات موجود")
            
            # فحص المحتوى
            cursor.execute("SELECT COUNT(*) FROM جدول_المجموعات")
            total_count = cursor.fetchone()[0]
            print(f"📊 إجمالي السجلات: {total_count}")
            
            cursor.execute("SELECT DISTINCT اسم_المجموعة FROM جدول_المجموعات WHERE اسم_المجموعة IS NOT NULL ORDER BY اسم_المجموعة")
            groups = cursor.fetchall()
            print(f"📊 عدد المجموعات المتاحة: {len(groups)}")
            
            if groups:
                print("📋 المجموعات المتاحة:")
                for i, group in enumerate(groups, 1):
                    print(f"   {i:2d}. {group[0]}")
                
                print("\n✅ النظام سيستخدم هذه المجموعات حصرياً")
                print("✅ لن يتم الرجوع إلى جدول_البيانات")
            else:
                print("⚠️ جدول المجموعات فارغ")
                print("💡 أضف مجموعات إلى الجدول لتفعيل الميزة")
        else:
            print("❌ جدول المجموعات غير موجود")
            print("💡 النظام سيعطل ميزة تغيير المجموعة")
            print("💡 لن يتم استخدام جدول_البيانات كبديل")
        
        conn.close()
        return table_exists is not None
        
    except Exception as e:
        print(f"❌ خطأ في الفحص: {e}")
        return False

def create_sample_groups_table():
    """إنشاء جدول المجموعات مع بيانات تجريبية"""
    print("\n🔧 إنشاء جدول المجموعات مع بيانات تجريبية")
    print("-" * 50)
    
    try:
        conn = sqlite3.connect('data.db')
        cursor = conn.cursor()
        
        # إنشاء الجدول
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS جدول_المجموعات (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                اسم_المجموعة TEXT NOT NULL UNIQUE,
                وصف_المجموعة TEXT,
                تاريخ_الإنشاء DATETIME DEFAULT CURRENT_TIMESTAMP,
                تاريخ_التحديث DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # إدراج مجموعات تجريبية
        sample_groups = [
            ("المجموعة الأولى", "مجموعة الطلاب المتفوقين"),
            ("المجموعة الثانية", "مجموعة الطلاب العاديين"),
            ("المجموعة الثالثة", "مجموعة الطلاب المتأخرين"),
            ("مجموعة الصباح", "طلاب الفترة الصباحية"),
            ("مجموعة المساء", "طلاب الفترة المسائية"),
            ("المجموعة الخاصة", "مجموعة ذات احتياجات خاصة")
        ]
        
        inserted_count = 0
        for group_name, description in sample_groups:
            try:
                cursor.execute("""
                    INSERT OR IGNORE INTO جدول_المجموعات (اسم_المجموعة, وصف_المجموعة)
                    VALUES (?, ?)
                """, (group_name, description))
                if cursor.rowcount > 0:
                    inserted_count += 1
            except Exception as e:
                print(f"خطأ في إدراج {group_name}: {e}")
        
        conn.commit()
        conn.close()
        
        print(f"✅ تم إنشاء الجدول وإدراج {inserted_count} مجموعة")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء الجدول: {e}")
        return False

def test_load_groups_function():
    """اختبار وظيفة تحميل المجموعات"""
    print("\n🧪 اختبار وظيفة تحميل المجموعات")
    print("-" * 40)
    
    try:
        conn = sqlite3.connect('data.db')
        cursor = conn.cursor()
        
        # محاكاة وظيفة load_groups_to_combo
        print("🔍 محاكاة وظيفة load_groups_to_combo...")
        
        # التحقق من وجود جدول المجموعات أولاً
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='جدول_المجموعات'")
        if not cursor.fetchone():
            print("❌ جدول المجموعات غير موجود")
            print("📋 النتيجة: القائمة ستحتوي على 'جدول المجموعات غير موجود'")
            print("🔒 القائمة ستكون معطلة")
            conn.close()
            return False

        # جلب المجموعات من جدول المجموعات فقط
        cursor.execute("SELECT DISTINCT اسم_المجموعة FROM جدول_المجموعات WHERE اسم_المجموعة IS NOT NULL ORDER BY اسم_المجموعة")
        groups = [row[0] for row in cursor.fetchall()]

        if groups:
            print("✅ تم تحميل المجموعات بنجاح")
            print("📋 القائمة ستحتوي على:")
            print("   1. اختر المجموعة الجديدة")
            for i, group in enumerate(groups, 2):
                print(f"   {i}. {group}")
            print("🔓 القائمة ستكون مفعلة")
        else:
            print("⚠️ لا توجد مجموعات في جدول المجموعات")
            print("📋 النتيجة: القائمة ستحتوي على 'لا توجد مجموعات متاحة'")
            print("🔒 القائمة ستكون معطلة")

        conn.close()
        return len(groups) > 0
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الوظيفة: {e}")
        return False

def show_usage_instructions():
    """عرض تعليمات الاستخدام"""
    print("\n📋 تعليمات الاستخدام:")
    print("=" * 30)
    
    print("🎯 لاستخدام ميزة تغيير المجموعة:")
    print("1. تأكد من وجود جدول المجموعات في قاعدة البيانات")
    print("2. أضف المجموعات المطلوبة إلى الجدول")
    print("3. استخدم التعديل الجماعي لتغيير مجموعة التلاميذ")
    
    print("\n🔧 إنشاء جدول المجموعات:")
    print("CREATE TABLE جدول_المجموعات (")
    print("    id INTEGER PRIMARY KEY AUTOINCREMENT,")
    print("    اسم_المجموعة TEXT NOT NULL UNIQUE,")
    print("    وصف_المجموعة TEXT,")
    print("    تاريخ_الإنشاء DATETIME DEFAULT CURRENT_TIMESTAMP,")
    print("    تاريخ_التحديث DATETIME DEFAULT CURRENT_TIMESTAMP")
    print(")")
    
    print("\n📝 إضافة مجموعة جديدة:")
    print("INSERT INTO جدول_المجموعات (اسم_المجموعة, وصف_المجموعة)")
    print("VALUES ('اسم المجموعة', 'وصف المجموعة')")
    
    print("\n⚠️ ملاحظات مهمة:")
    print("- النظام لن يستخدم جدول_البيانات كبديل")
    print("- إذا لم يوجد جدول المجموعات، ستكون الميزة معطلة")
    print("- تأكد من وجود مجموعات في الجدول قبل الاستخدام")

def main():
    """الوظيفة الرئيسية"""
    print("🚀 اختبار استخدام جدول المجموعات حصرياً")
    print("=" * 60)
    
    # اختبار الوضع الحالي
    current_ok = test_groups_table_exclusive()
    
    # إنشاء الجدول إذا لم يكن موجود
    if not current_ok:
        print("\n💡 هل تريد إنشاء جدول المجموعات مع بيانات تجريبية؟")
        create_ok = create_sample_groups_table()
        if create_ok:
            # إعادة اختبار بعد الإنشاء
            current_ok = test_groups_table_exclusive()
    
    # اختبار وظيفة التحميل
    load_ok = test_load_groups_function()
    
    # عرض التعليمات
    show_usage_instructions()
    
    print("\n" + "=" * 60)
    print("📊 نتائج الاختبار:")
    print(f"📋 جدول المجموعات: {'✅ موجود ومفعل' if current_ok else '❌ غير موجود أو فارغ'}")
    print(f"🔧 وظيفة التحميل: {'✅ تعمل بشكل صحيح' if load_ok else '❌ لا تعمل'}")
    
    if current_ok and load_ok:
        print("\n🎉 النظام جاهز لاستخدام جدول المجموعات حصرياً!")
        print("✅ لن يتم الرجوع إلى جدول_البيانات")
        print("✅ ميزة تغيير المجموعة مفعلة")
    else:
        print("\n💥 يوجد مشاكل تحتاج إلى حل!")
        print("💡 تأكد من وجود جدول المجموعات وإضافة مجموعات إليه")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
