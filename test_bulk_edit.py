#!/usr/bin/env python
# -*- coding: utf-8 -*-

import sys
import os
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import Qt

def test_bulk_edit_button():
    """اختبار زر التعديل الجماعي الجديد"""
    
    print("🔍 اختبار زر التعديل الجماعي الجديد...")
    print("📋 مميزات الزر الجديد:")
    print("   ✅ تعديل القسم لعدة تلاميذ دفعة واحدة")
    print("   ✅ تعديل واجبات التسجيل لعدة تلاميذ")
    print("   ✅ تعديل الواجب الشهري لعدة تلاميذ")
    print("   ✅ إعادة حساب المبلغ النهائي الشهري تلقائياً")
    print("   ✅ واجهة مستخدم سهلة ومنظمة")
    print("   ✅ تأكيد قبل التطبيق")
    print("   ✅ عرض نتائج العملية")
    print()
    
    try:
        # إنشاء تطبيق PyQt5
        app = QApplication(sys.argv)
        app.setLayoutDirection(Qt.RightToLeft)
        
        # استيراد النافذة الرئيسية
        from sub252_window import DataViewWindow
        
        # إنشاء النافذة
        window = DataViewWindow()
        
        # عرض النافذة
        window.show()
        
        print("✅ تم تشغيل النافذة الرئيسية بنجاح!")
        print("🔍 ابحث عن زر '🔄 التعديل الجماعي' في النافذة")
        print("📋 خطوات الاختبار:")
        print("   1. اختر مجموعة من قائمة التصفية")
        print("   2. حدد عدة تلاميذ من الجدول (أكثر من واحد)")
        print("   3. اضغط على زر 'التعديل الجماعي'")
        print("   4. اختر التغييرات المطلوبة:")
        print("      • تغيير القسم (اختياري)")
        print("      • تغيير واجبات التسجيل (اختياري)")
        print("      • تغيير الواجب الشهري (اختياري)")
        print("   5. اضغط على 'تطبيق التغييرات'")
        print("   6. أكد التطبيق")
        print("   7. ستظهر نتائج العملية")
        print()
        print("⚠️ ملاحظات مهمة:")
        print("   • يجب تحديد أكثر من تلميذ واحد")
        print("   • يجب تحديد تغيير واحد على الأقل")
        print("   • سيتم إعادة حساب المبلغ النهائي الشهري تلقائياً")
        print("   • العملية قابلة للتراجع من خلال التعديل الفردي")
        
        # تشغيل التطبيق
        sys.exit(app.exec_())
        
    except Exception as e:
        print(f"❌ خطأ في تشغيل النافذة الرئيسية: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("=" * 80)
    print("🧪 اختبار زر التعديل الجماعي الجديد")
    print("=" * 80)
    
    test_bulk_edit_button()
