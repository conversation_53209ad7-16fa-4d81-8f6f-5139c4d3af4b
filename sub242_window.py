#!/usr/bin/env python
# -*- coding: utf-8 -*-

import sys
import os
import sqlite3
from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QTabWidget, QLabel, QLineEdit, QPushButton, QTableWidget,
    QTableWidgetItem, QFrame, QMessageBox, QSpinBox, QComboBox,
    QDateEdit, QFormLayout, QGroupBox, QGridLayout, QHeaderView,
    QTextEdit, QDoubleSpinBox, QCheckBox
)
from PyQt5.QtGui import QFont, QIcon, QColor, QPixmap
from PyQt5.QtCore import Qt, QDate, QSize
from datetime import datetime
import json

class MonthlyDutiesWindow(QMainWindow):
    """نافذة واجبات التسجيل والواجبات الشهرية"""
    
    def __init__(self, parent=None, db_path="data.db"):
        super().__init__(parent)
        self.db_path = db_path
        
        # إعدادات أحجام الخطوط المركزية - يمكنك تغيير الأرقام هنا للتحكم في جميع عناصر مجموعة الاتصال
        self.CONTACT_INPUT_FONT_SIZE = 14      # حجم خط حقول الإدخال النصية
        self.CONTACT_COMBO_FONT_SIZE = 14      # حجم خط القوائم المنسدلة
        self.CONTACT_TEXTAREA_FONT_SIZE = 14   # حجم خط منطقة النص (الملاحظات)
        
        # إعدادات أحجام الخطوط لمجموعة التمدرس - يمكنك تغيير الأرقام هنا للتحكم في جميع عناصر مجموعة التمدرس
        self.SCHOOLING_COMBO_FONT_SIZE = 14    # حجم خط القوائم المنسدلة في التمدرس (المجموعة)
        self.SCHOOLING_EDITABLE_COMBO_FONT_SIZE = 14  # حجم خط مربعات التحرير والسرد في التمدرس (القسم والمؤسسة)
        self.SCHOOLING_BUTTON_FONT_SIZE = 14   # حجم خط أزرار الإضافة في التمدرس
        self.SCHOOLING_COMBO_HEIGHT = 40       # ارتفاع القوائم المنسدلة في مجموعة التمدرس (بالبكسل)
        
        # إعدادات واجبات التسجيل
        self.REGISTRATION_INPUT_FONT_SIZE = 15  # حجم خط حقول واجبات التسجيل
        self.REGISTRATION_INPUT_HEIGHT = 40     # ارتفاع حقول واجبات التسجيل (بالبكسل)
        
        # إعدادات الواجبات الشهرية
        self.MONTHLY_INPUT_FONT_SIZE = 15       # حجم خط حقل الواجب الشهري
        self.MONTHLY_INPUT_HEIGHT = 40          # ارتفاع حقل الواجب الشهري (بالبكسل)

        self.setupUI()
        self.setup_database()
        # إزالة تحميل البيانات عند الفتح ليكون في وضع إضافة
        # self.load_data()
        
        # إعداد النافذة لوضع الإضافة
        self.setup_add_mode()

    def setupUI(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("واجبات التسجيل والواجبات الشهرية")
        self.setFixedSize(900, 700)
        self.setLayoutDirection(Qt.RightToLeft)
        
        # تطبيق نمط احترافي للنافذة الرئيسية
        self.setStyleSheet("""
            QMainWindow {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 1, y2: 1,
                    stop: 0 #f8f9fc,
                    stop: 1 #e9ecef
                );
            }
        """)
        
        # إنشاء الواجهة المركزية
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(15, 15, 15, 15)
        main_layout.setSpacing(10)
        
        # العنوان الرئيسي - يمكنك تغيير حجم الخط هنا
        title_label = QLabel("واجبات التسجيل والواجبات الشهرية")
        title_label.setFont(QFont("Calibri", 15, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 1, y2: 0,
                    stop: 0 #3498db,
                    stop: 0.5 #2980b9,
                    stop: 1 #3498db
                );
                color: white;
                padding: 20px;
                border-radius: 15px;
                font-weight: bold;
                margin-bottom: 10px;
                box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
            }
        """)
        main_layout.addWidget(title_label)
        
        # إنشاء التبويبات - حجم خط أسماء التبويبات
        self.tab_widget = QTabWidget()
        self.tab_widget.setFont(QFont("Calibri", 15, QFont.Bold))  # هنا تتحكم في حجم خط أسماء التبويبات الأساسي
        
        # تطبيق نمط احترافي للتبويبات
        self.tab_widget.setStyleSheet("""
            QTabWidget {
                background-color: transparent;
                border: none;
            }
            
            QTabWidget::pane {
                border: 3px solid #3498db;
                border-radius: 15px;
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #ffffff,
                    stop: 1 #f8f9fa
                );
                padding: 10px;
                margin-top: 5px;
            }
            
            QTabWidget::tab-bar {
                alignment: center;
                left: 10px;
            }
            
            QTabBar::tab {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #ecf0f1,
                    stop: 1 #d5dbdb
                );
                color: #2c3e50;
                padding: 15px 25px;
                margin: 2px;
                border-radius: 12px 12px 0px 0px;
                font-family: 'Calibri';
                font-size: 18px;    /* هنا تتحكم في حجم خط التبويبات في CSS */
                font-weight: bold;
                min-width: 140px;
                border: 2px solid #bdc3c7;
                border-bottom: none;
            }
            
            QTabBar::tab:selected {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #3498db,
                    stop: 0.5 #2980b9,
                    stop: 1 #3498db
                );
                color: white;
                border: 2px solid #2980b9;
                border-bottom: none;
                margin-bottom: -2px;
                padding-bottom: 17px;
            }
            
            QTabBar::tab:hover:!selected {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #e8f4fd,
                    stop: 1 #d6eaf8
                );
                color: #2980b9;
                border: 2px solid #85c1e9;
                border-bottom: none;
            }
            
            QTabBar::tab:first {
                margin-left: 10px;
            }
            
            QTabBar::tab:last {
                margin-right: 10px;
            }
            
            /* إضافة تأثير الظل للتبويبات */
            QTabBar::tab:selected {
                box-shadow: 0 -3px 10px rgba(52, 152, 219, 0.3);
            }
        """)

        # إضافة التبويبات
        self.setup_contact_tab()
        self.setup_schooling_tab()
        self.setup_registration_tab()
        self.setup_monthly_duties_tab()
        
        main_layout.addWidget(self.tab_widget)
        
    def setup_contact_tab(self):
        """إعداد تبويب معلومات الاتصال"""
        contact_tab = QWidget()
        layout = QVBoxLayout(contact_tab)
        layout.setContentsMargins(5, 5, 5, 5)
        layout.setSpacing(5)
        
        # إطار معلومات الاتصال - عنوان المجموعة
        contact_frame = QGroupBox("معلومات الاتصال")
        contact_frame.setFont(QFont("Calibri", 16, QFont.Bold))
        
        contact_layout = QFormLayout(contact_frame)
        contact_layout.setSpacing(5)
        
        # إنشاء حقول معلومات الاتصال
        self.student_name_input = self.create_contact_input()
        self.student_code_input = self.create_contact_input()
        self.phone_input = self.create_contact_input()
        self.phone2_input = self.create_contact_input()
        
        # حقل النوع
        self.gender_combo = QComboBox()
        self.gender_combo.addItems(["ذكر", "أنثى"])
        self.gender_combo.setFont(QFont("Calibri", self.CONTACT_COMBO_FONT_SIZE, QFont.Bold))
        
        self.address_input = QTextEdit()
        self.address_input.setMinimumHeight(50)
        self.address_input.setMaximumHeight(100)
        self.address_input.setFont(QFont("Calibri", self.CONTACT_TEXTAREA_FONT_SIZE, QFont.Bold))

        # إضافة الحقول إلى النموذج
        contact_layout.addRow(self.create_styled_label("اسم التلميذ:"), self.student_name_input)
        contact_layout.addRow(self.create_styled_label("رمز التلميذ:"), self.student_code_input)
        contact_layout.addRow(self.create_styled_label("النوع:"), self.gender_combo)
        contact_layout.addRow(self.create_styled_label("رقم الهاتف الأول:"), self.phone_input)
        contact_layout.addRow(self.create_styled_label("رقم الهاتف الثاني:"), self.phone2_input)
        contact_layout.addRow(self.create_styled_label("ملاحظات :"), self.address_input)
        
        layout.addWidget(contact_frame)
        
        # أزرار العمليات
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(15)
        
        save_contact_btn = self.create_styled_button("💾 حفظ المعلومات", "#4CAF50")
        save_contact_btn.clicked.connect(self.save_contact_info)
        
        clear_contact_btn = self.create_styled_button("🗑️ مسح البيانات", "#F44336")
        clear_contact_btn.clicked.connect(self.clear_contact_info)
        
        # إضافة زر حفظ جميع البيانات
        save_all_btn = self.create_styled_button("💾 حفظ جميع البيانات", "#2E7D32")
        save_all_btn.clicked.connect(self.save_all_data)
        
        buttons_layout.addWidget(save_contact_btn)
        buttons_layout.addWidget(clear_contact_btn)
        buttons_layout.addWidget(save_all_btn)
        buttons_layout.addStretch()
        
        layout.addLayout(buttons_layout)
        layout.addStretch()
        
        self.tab_widget.addTab(contact_tab, "معلومات الاتصال")
        
    def setup_schooling_tab(self):
        """إعداد تبويب معلومات التمدرس"""
        schooling_tab = QWidget()
        layout = QVBoxLayout(schooling_tab)
        layout.setContentsMargins(5, 5, 5, 5)
        layout.setSpacing(5)
        
        # إطار معلومات التمدرس - عنوان المجموعة
        schooling_frame = QGroupBox("معلومات التمدرس")
        schooling_frame.setFont(QFont("Calibri", 16, QFont.Bold))
        
        schooling_layout = QFormLayout(schooling_frame)
        schooling_layout.setSpacing(5)
        
        # حقل اسم المجموعة مع قائمة منسدلة وزر إضافة
        group_layout = QHBoxLayout()
        self.group_combo = QComboBox()
        self.group_combo.addItems([
            "مجموعة مفتوحة",
            "مجموعة مغلقة", 
            "مجموعة المباريات",
            "مجموعة خاصة"
        ])
        self.group_combo.setFont(QFont("Calibri", self.SCHOOLING_COMBO_FONT_SIZE, QFont.Bold))
        self.group_combo.setMinimumHeight(self.SCHOOLING_COMBO_HEIGHT)
        
        add_group_btn = self.create_schooling_button("➕ إضافة مجموعة", "#4CAF50")
        add_group_btn.clicked.connect(self.add_new_group)
        
        group_layout.addWidget(self.group_combo, 3)
        group_layout.addWidget(add_group_btn, 1)
        
        # حقل القسم مع قائمة منسدلة وزر إضافة
        section_layout = QHBoxLayout()
        self.section_combo = QComboBox()
        self.section_combo.setEditable(True)
        self.section_combo.addItems([
            "القسم الأول",
            "القسم الثاني",
            "القسم الثالث",
            "القسم الرابع",
            "القسم الخامس",
            "القسم السادس"
        ])
        self.section_combo.setFont(QFont("Calibri", self.SCHOOLING_EDITABLE_COMBO_FONT_SIZE, QFont.Bold))
        # تطبيق الخط على حقل النص القابل للتحرير بداخل القائمة المنسدلة
        self.section_combo.lineEdit().setFont(QFont("Calibri", self.SCHOOLING_EDITABLE_COMBO_FONT_SIZE, QFont.Bold))
        self.section_combo.setMinimumHeight(self.SCHOOLING_COMBO_HEIGHT)
        
        add_section_btn = self.create_schooling_button("➕ إضافة قسم", "#2196F3")
        add_section_btn.clicked.connect(self.add_new_section)
        
        section_layout.addWidget(self.section_combo, 3)
        section_layout.addWidget(add_section_btn, 1)
        
        # حقل المؤسسة الأصلية مع قائمة منسدلة وزر إضافة
        institution_layout = QHBoxLayout()
        self.institution_combo = QComboBox()
        self.institution_combo.setEditable(True)
        self.institution_combo.addItems([
            "مؤسسة عمومية",
            "مؤسسة خاصة",
            "مؤسسة أجنبية",
            "التعليم الأصلي"
        ])
        self.institution_combo.setFont(QFont("Calibri", self.SCHOOLING_EDITABLE_COMBO_FONT_SIZE, QFont.Bold))
        # تطبيق الخط على حقل النص القابل للتحرير بداخل القائمة المنسدلة
        self.institution_combo.lineEdit().setFont(QFont("Calibri", self.SCHOOLING_EDITABLE_COMBO_FONT_SIZE, QFont.Bold))
        self.institution_combo.setMinimumHeight(self.SCHOOLING_COMBO_HEIGHT)

        add_institution_btn = self.create_schooling_button("➕ إضافة مؤسسة", "#FF9800")
        add_institution_btn.clicked.connect(self.add_new_institution)
        
        institution_layout.addWidget(self.institution_combo, 3)
        institution_layout.addWidget(add_institution_btn, 1)
        
        # إضافة الحقول إلى النموذج
        schooling_layout.addRow(self.create_styled_label("اسم المجموعة:"), group_layout)
        schooling_layout.addRow(self.create_styled_label("القسم:"), section_layout)
        schooling_layout.addRow(self.create_styled_label("المؤسسة الأصلية:"), institution_layout)
        
        layout.addWidget(schooling_frame)
        
        # أزرار العمليات
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(15)
        
        save_schooling_btn = self.create_styled_button("💾 حفظ معلومات التمدرس", "#2196F3")
        save_schooling_btn.clicked.connect(self.save_schooling_info)
        
        clear_schooling_btn = self.create_styled_button("🗑️ مسح البيانات", "#F44336")
        clear_schooling_btn.clicked.connect(self.clear_schooling_info)
        
        # إضافة زر حفظ جميع البيانات
        save_all_btn2 = self.create_styled_button("💾 حفظ جميع البيانات", "#2E7D32")
        save_all_btn2.clicked.connect(self.save_all_data)
        
        buttons_layout.addWidget(save_schooling_btn)
        buttons_layout.addWidget(clear_schooling_btn)
        buttons_layout.addWidget(save_all_btn2)
        buttons_layout.addStretch()

        layout.addLayout(buttons_layout)
        layout.addStretch()
        
        self.tab_widget.addTab(schooling_tab, "معلومات التمدرس")
        
    def setup_registration_tab(self):
        """إعداد تبويب واجبات التسجيل"""
        registration_tab = QWidget()
        layout = QVBoxLayout(registration_tab)
        layout.setContentsMargins(5, 5, 5, 5)
        layout.setSpacing(5)
        
        # إطار واجبات التسجيل - عنوان المجموعة
        registration_frame = QGroupBox("واجبات التسجيل")
        registration_frame.setFont(QFont("Calibri", 16, QFont.Bold))
        
        reg_layout = QFormLayout(registration_frame)
        reg_layout.setSpacing(5)
        
        # حقول واجبات التسجيل - حقول عملة مباشرة
        self.total_amount_input = QLineEdit()
        self.total_amount_input.setPlaceholderText("أدخل المبلغ الإجمالي بالدرهم")
        self.total_amount_input.setFont(QFont("Calibri", self.REGISTRATION_INPUT_FONT_SIZE, QFont.Bold))
        self.total_amount_input.setMinimumHeight(self.REGISTRATION_INPUT_HEIGHT)
        self.total_amount_input.textChanged.connect(self.calculate_installment_amount)
        # تطبيق تنسيق للمبلغ أثناء الكتابة
        self.total_amount_input.textChanged.connect(self.format_currency_input)
        
        self.installments_input = QSpinBox()
        self.installments_input.setMinimum(1)
        self.installments_input.setMaximum(12)
        self.installments_input.setValue(1)
        self.installments_input.setFont(QFont("Calibri", self.REGISTRATION_INPUT_FONT_SIZE, QFont.Bold))
        self.installments_input.setMinimumHeight(self.REGISTRATION_INPUT_HEIGHT)
        self.installments_input.valueChanged.connect(self.calculate_installment_amount)
        
        self.installment_amount_display = QLabel("0.00 درهم")
        self.installment_amount_display.setFont(QFont("Calibri", self.REGISTRATION_INPUT_FONT_SIZE, QFont.Bold))
        self.installment_amount_display.setAlignment(Qt.AlignCenter)
        self.installment_amount_display.setMinimumHeight(self.REGISTRATION_INPUT_HEIGHT)
        self.installment_amount_display.setStyleSheet("""
            QLabel {
                background-color: #f8f9fa;
                border: 2px solid #e9ecef;
                border-radius: 8px;
                padding: 8px;
                color: #2c3e50;
            }
        """)

        reg_layout.addRow(self.create_styled_label("إجمالي المبلغ:"), self.total_amount_input)
        reg_layout.addRow(self.create_styled_label("عدد الأقساط:"), self.installments_input)
        reg_layout.addRow(self.create_styled_label("مبلغ القسط الواحد:"), self.installment_amount_display)
        
        layout.addWidget(registration_frame)
        
        # أزرار العمليات
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(15)
        
        save_reg_btn = self.create_styled_button("💾 حفظ واجبات التسجيل", "#2196F3")
        save_reg_btn.clicked.connect(self.save_registration_duties)
        
        print_receipt_btn = self.create_styled_button("🖨️ طباعة التوصيل", "#FF6F00")
        print_receipt_btn.clicked.connect(self.print_registration_receipt)
        
        # إضافة زر حفظ جميع البيانات
        save_all_btn3 = self.create_styled_button("💾 حفظ جميع البيانات", "#2E7D32")
        save_all_btn3.clicked.connect(self.save_all_data)
        
        buttons_layout.addWidget(save_reg_btn)
        buttons_layout.addWidget(print_receipt_btn)
        buttons_layout.addWidget(save_all_btn3)
        buttons_layout.addStretch()

        layout.addLayout(buttons_layout)
        layout.addStretch()
        
        self.tab_widget.addTab(registration_tab, "واجبات التسجيل")
        
    def setup_monthly_duties_tab(self):
        """إعداد تبويب الواجبات الشهرية"""
        monthly_tab = QWidget()
        layout = QVBoxLayout(monthly_tab)
        layout.setContentsMargins(5, 5, 5, 5)
        layout.setSpacing(5)
        
        # إطار الواجبات الشهرية - عنوان المجموعة
        monthly_frame = QGroupBox("الواجبات الشهرية")
        monthly_frame.setFont(QFont("Calibri", 17, QFont.Bold))
        
        monthly_layout = QVBoxLayout(monthly_frame)
        monthly_layout.setSpacing(15)
        
        # معلومات الواجب الشهري - حقل عملة مباشر
        form_layout = QFormLayout()
        form_layout.setSpacing(20)
        
        self.monthly_duty_input = QLineEdit()
        self.monthly_duty_input.setPlaceholderText("أدخل مبلغ الواجب الشهري بالدرهم")
        self.monthly_duty_input.setFont(QFont("Calibri", self.MONTHLY_INPUT_FONT_SIZE, QFont.Bold))
        self.monthly_duty_input.setMinimumHeight(self.MONTHLY_INPUT_HEIGHT)
        self.monthly_duty_input.textChanged.connect(self.calculate_total_monthly)
        self.monthly_duty_input.textChanged.connect(self.format_monthly_currency_input)
        
        form_layout.addRow(self.create_styled_label("الواجب الشهري:"), self.monthly_duty_input)
        
        monthly_layout.addLayout(form_layout)
        
        # إطار الأشهر - عنوان فرعي
        months_frame = QGroupBox("الأشهر التي سيدفع فيها الطالب")
        months_frame.setFont(QFont("Calibri", 15, QFont.Bold))
        
        months_layout = QGridLayout(months_frame)
        months_layout.setSpacing(15)
        
        # إنشاء مربعات اختيار للأشهر مع الترتيب الجديد
        self.month_checkboxes = {}
        months = [
            "شتنبر", "أكتوبر", "نونبر", "دجنبر", "يناير", "فبراير",
            "مارس", "أبريل", "ماي", "يونيو", "يوليوز", "غشت"
        ]
        
        # تنظيم الأشهر في صفين (6 أشهر في كل صف)
        for i, month in enumerate(months):
            checkbox = QCheckBox(month)
            checkbox.setFont(QFont("Calibri", 15, QFont.Bold))
            checkbox.stateChanged.connect(self.calculate_total_monthly)
            
            row = i // 6  # صفين: الصف الأول للأشهر 0-5، الصف الثاني للأشهر 6-11
            col = i % 6   # 6 أعمدة في كل صف
            months_layout.addWidget(checkbox, row, col)
            
            self.month_checkboxes[month] = checkbox
        
        monthly_layout.addWidget(months_frame)
        
        # عرض المبلغ النهائي
        total_layout = QFormLayout()
        self.total_monthly_display = QLabel("0.00 درهم")
        self.total_monthly_display.setFont(QFont("Calibri", 18, QFont.Bold))
        self.total_monthly_display.setAlignment(Qt.AlignCenter)
        self.total_monthly_display.setMinimumHeight(self.MONTHLY_INPUT_HEIGHT)
        self.total_monthly_display.setStyleSheet("""
            QLabel {
                background-color: #f8f9fa;
                border: 2px solid #e9ecef;
                border-radius: 8px;
                padding: 8px;
                color: #2c3e50;
            }
        """)
        
        total_layout.addRow(self.create_styled_label("المبلغ النهائي:"), self.total_monthly_display)
        monthly_layout.addLayout(total_layout)
        
        layout.addWidget(monthly_frame)
        
        # أزرار العمليات
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(15)
        
        save_monthly_btn = self.create_styled_button("💾 حفظ الواجبات الشهرية", "#2196F3")
        save_monthly_btn.clicked.connect(self.save_monthly_duties)
        
        print_monthly_receipt_btn = self.create_styled_button("🖨️ طباعة التوصيل", "#FF6F00")
        print_monthly_receipt_btn.clicked.connect(self.print_monthly_receipt)
        
        # إضافة زر حفظ جميع البيانات
        save_all_btn4 = self.create_styled_button("💾 حفظ جميع البيانات", "#2E7D32")
        save_all_btn4.clicked.connect(self.save_all_data)
        
        buttons_layout.addWidget(save_monthly_btn)
        buttons_layout.addWidget(print_monthly_receipt_btn)
        buttons_layout.addWidget(save_all_btn4)
        buttons_layout.addStretch()

        layout.addLayout(buttons_layout)
        layout.addStretch()
        
        self.tab_widget.addTab(monthly_tab, "الواجبات الشهرية")
        
    def create_contact_input(self):
        """إنشاء حقل إدخال خاص بمجموعة الاتصال"""
        input_field = QLineEdit()
        input_field.setFont(QFont("Calibri", self.CONTACT_INPUT_FONT_SIZE, QFont.Bold))
        return input_field

    def create_styled_input(self):
        """إنشاء حقل إدخال منسق للمجموعات الأخرى"""
        input_field = QLineEdit()
        input_field.setFont(QFont("Calibri", 15, QFont.Bold))
        return input_field
        
    def create_styled_label(self, text):
        """إنشاء تسمية منسقة - هنا تتحكم في حجم خط جميع المسميات (Labels)"""
        label = QLabel(text)
        label.setFont(QFont("Calibri", 15, QFont.Bold))  # غير الرقم 16 لتغيير حجم خط جميع المسميات
        return label
        
    def create_styled_button(self, text, color="#2196F3"):
        """إنشاء زر منسق بتصميم احترافي"""
        button = QPushButton(text)
        button.setFont(QFont("Calibri", 15, QFont.Bold))
        button.setMinimumHeight(45)
        button.setMinimumWidth(200)
        button.setStyleSheet(f"""
            QPushButton {{
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 {color},
                    stop: 1 {self.darken_color(color, 20)}
                );
                color: white;
                border: none;
                border-radius: 12px;
                padding: 12px 20px;
                font-weight: bold;
                text-align: center;
            }}
            QPushButton:hover {{
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 {self.lighten_color(color, 10)},
                    stop: 1 {color}
                );
            }}
            QPushButton:pressed {{
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 {self.darken_color(color, 30)},
                    stop: 1 {self.darken_color(color, 40)}
                );
            }}
            QPushButton:disabled {{
                background: #CCCCCC;
                color: #666666;
            }}
        """)
        return button

    def create_schooling_button(self, text, color):
        """إنشاء زر خاص بمجموعة التمدرس"""
        button = QPushButton(text)
        button.setFont(QFont("Calibri", self.SCHOOLING_BUTTON_FONT_SIZE, QFont.Bold))
        button.setStyleSheet(f"""
            QPushButton {{
                background-color: {color};
                color: white;
                border: none;
                border-radius: 8px;
                padding: 8px 15px;
                font-weight: bold;
                min-height: 30px;
            }}
            QPushButton:hover {{
                background-color: {self.darken_color(color, 20)};
            }}
            QPushButton:pressed {{
                background-color: {self.darken_color(color, 40)};
            }}
        """)
        return button

    def lighten_color(self, color, amount):
        """تفتيح اللون"""
        if color.startswith('#'):
            color = color[1:]
        
        r = min(255, int(color[0:2], 16) + amount)
        g = min(255, int(color[2:4], 16) + amount)
        b = min(255, int(color[4:6], 16) + amount)
        
        return f"#{r:02x}{g:02x}{b:02x}"

    def darken_color(self, color, amount):
        """تغميق اللون"""
        if color.startswith('#'):
            color = color[1:]
        
        r = max(0, int(color[0:2], 16) - amount)
        g = max(0, int(color[2:4], 16) - amount)
        b = max(0, int(color[4:6], 16) - amount)
        
        return f"#{r:02x}{g:02x}{b:02x}"

    def get_groupbox_style(self):
        """الحصول على نمط المجموعات"""
        return """
            QGroupBox {
                color: #1976d2;
                border: 2px solid #1976d2;
                border-radius: 8px;
                padding-top: 20px;
                margin-top: 15px;
                background-color: white;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 15px;
                padding: 0 15px 0 15px;
                background-color: white;
                font-size: 18px;
                font-weight: bold;
            }
        """
        
    def adjust_color(self, color, amount):
        """تعديل لون للحصول على تدرج"""
        # تحويل لون hex إلى RGB وتعديله
        if color.startswith('#'):
            color = color[1:]
        
        r = max(0, min(255, int(color[0:2], 16) + amount))
        g = max(0, min(255, int(color[2:4], 16) + amount))
        b = max(0, min(255, int(color[4:6], 16) + amount))
        
        return f"#{r:02x}{g:02x}{b:02x}"
        
    def setup_database(self):
        """إعداد قاعدة البيانات"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # إنشاء الجدول الموحد لجميع البيانات
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS جدول_البيانات (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    
                    -- معلومات الاتصال
                    اسم_التلميذ TEXT NOT NULL,
                    رمز_التلميذ TEXT UNIQUE,
                    النوع TEXT,
                    رقم_الهاتف_الأول TEXT,
                    رقم_الهاتف_الثاني TEXT,
                    ملاحظات TEXT,
                    
                    -- معلومات التمدرس
                    اسم_المجموعة TEXT,
                    القسم TEXT,
                    المؤسسة_الاصلية TEXT,
                    
                    -- واجبات التسجيل
                    اجمالي_مبلغ_التسجيل REAL,
                    عدد_الاقساط INTEGER,
                    مبلغ_القسط REAL,
                    
                    -- الواجبات الشهرية
                    الواجب_الشهري REAL,
                    الاشهر_المحددة TEXT,
                    المبلغ_النهائي_الشهري REAL,
                    
                    -- تواريخ النظام
                    تاريخ_الانشاء DATETIME DEFAULT CURRENT_TIMESTAMP,
                    تاريخ_التحديث DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # الاحتفاظ بالجداول القديمة للتوافق مع النسخة السابقة (اختياري)
            # يمكن حذف هذا القسم إذا لم تعد تحتاج للجداول القديمة
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في إعداد قاعدة البيانات: {str(e)}")
            
    def load_data(self):
        """تحميل البيانات من الجدول الموحد"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # تحميل آخر سجل من جدول البيانات الموحد
            cursor.execute("SELECT * FROM جدول_البيانات ORDER BY id DESC LIMIT 1")
            record = cursor.fetchone()
            
            if record:
                # ملء حقول معلومات الاتصال
                self.student_name_input.setText(record[1] or "")  # اسم_التلميذ
                self.student_code_input.setText(record[2] or "")  # رمز_التلميذ
                
                # تعيين النوع
                if record[3]:  # النوع
                    gender_index = self.gender_combo.findText(record[3])
                    if gender_index >= 0:
                        self.gender_combo.setCurrentIndex(gender_index)
                
                self.phone_input.setText(record[4] or "")  # رقم_الهاتف_الأول
                self.phone2_input.setText(record[5] or "")  # رقم_الهاتف_الثاني
                self.address_input.setPlainText(record[6] or "")  # ملاحظات
                
                # ملء حقول معلومات التمدرس
                group_text = record[7] or ""  # اسم_المجموعة
                if group_text:
                    group_index = self.group_combo.findText(group_text)
                    if group_index >= 0:
                        self.group_combo.setCurrentIndex(group_index)
                
                section_text = record[8] or ""  # القسم
                if section_text:
                    self.section_combo.setCurrentText(section_text)
                
                institution_text = record[9] or ""  # المؤسسة_الاصلية
                if institution_text:
                    self.institution_combo.setCurrentText(institution_text)
                
                # ملء حقول واجبات التسجيل
                self.total_amount_input.setText(str(record[10] or 0))  # اجمالي_مبلغ_التسجيل
                self.installments_input.setValue(record[11] or 1)  # عدد_الاقساط
                
                # ملء حقول الواجبات الشهرية
                self.monthly_duty_input.setText(str(record[13] or 0))  # الواجب_الشهري
                
                # تحميل الأشهر المحددة
                if record[14]:  # الاشهر_المحددة
                    try:
                        selected_months = json.loads(record[14])
                        for month, checkbox in self.month_checkboxes.items():
                            checkbox.setChecked(month in selected_months)
                    except:
                        pass
                        
            conn.close()
            
            # تحديث الحسابات
            self.calculate_installment_amount()
            self.calculate_total_monthly()
            
        except Exception as e:
            print(f"خطأ في تحميل البيانات: {str(e)}")

    def format_currency_input(self):
        """تنسيق إدخال العملة"""
        sender = self.sender()
        text = sender.text()
        
        # إزالة كل شيء عدا الأرقام والنقطة العشرية
        import re
        cleaned_text = re.sub(r'[^0-9.]', '', text)
        
        # التأكد من وجود نقطة عشرية واحدة فقط
        parts = cleaned_text.split('.')
        if len(parts) > 2:
            cleaned_text = parts[0] + '.' + ''.join(parts[1:])
        
        # تحديد النص إذا تغير
        if cleaned_text != text:
            cursor_pos = sender.cursorPosition()
            sender.setText(cleaned_text)
            sender.setCursorPosition(min(cursor_pos, len(cleaned_text)))
    
    def calculate_installment_amount(self):
        """حساب مبلغ القسط الواحد"""
        try:
            # الحصول على المبلغ الإجمالي من حقل النص
            total_amount_text = self.total_amount_input.text().strip()
            if not total_amount_text:
                total_amount = 0.0
            else:
                total_amount = float(total_amount_text)
            
            installments = self.installments_input.value()
            
            if installments > 0 and total_amount > 0:
                installment_amount = total_amount / installments
                self.installment_amount_display.setText(f"{installment_amount:.2f} درهم")
            else:
                self.installment_amount_display.setText("0.00 درهم")
        except ValueError:
            self.installment_amount_display.setText("0.00 درهم")
            
    def format_monthly_currency_input(self):
        """تنسيق إدخال العملة للواجب الشهري"""
        sender = self.sender()
        text = sender.text()
        
        # إزالة كل شيء عدا الأرقام والنقطة العشرية
        import re
        cleaned_text = re.sub(r'[^0-9.]', '', text)
        
        # التأكد من وجود نقطة عشرية واحدة فقط
        parts = cleaned_text.split('.')
        if len(parts) > 2:
            cleaned_text = parts[0] + '.' + ''.join(parts[1:])
        
        # تحديد النص إذا تغير
        if cleaned_text != text:
            cursor_pos = sender.cursorPosition()
            sender.setText(cleaned_text)
            sender.setCursorPosition(min(cursor_pos, len(cleaned_text)))
            
    def calculate_total_monthly(self):
        """حساب المبلغ النهائي للواجبات الشهرية"""
        try:
            # الحصول على مبلغ الواجب الشهري من حقل النص
            monthly_duty_text = self.monthly_duty_input.text().strip()
            if not monthly_duty_text:
                monthly_duty = 0.0
            else:
                monthly_duty = float(monthly_duty_text)
            
            selected_months = self.get_selected_months()
            total_monthly = monthly_duty * len(selected_months)
            self.total_monthly_display.setText(f"{total_monthly:.2f} درهم")
        except ValueError:
            self.total_monthly_display.setText("0.00 درهم")
        
    def get_selected_months(self):
        """الحصول على الأشهر المحددة"""
        selected_months = []
        for month, checkbox in self.month_checkboxes.items():
            if checkbox.isChecked():
                selected_months.append(month)
        return selected_months
        
    def save_contact_info(self):
        """حفظ معلومات الاتصال في الجدول الموحد"""
        try:
            # التحقق من وجود البيانات الأساسية
            if not self.student_name_input.text().strip():
                QMessageBox.warning(self, "تحذير", "يرجى إدخال اسم التلميذ.")
                return
                
            if not self.student_code_input.text().strip():
                QMessageBox.warning(self, "تحذير", "يرجى إدخال رمز التلميذ.")
                return
            
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # التحقق من وجود الرمز مسبقاً
            cursor.execute("SELECT id FROM جدول_البيانات WHERE رمز_التلميذ = ?", 
                          (self.student_code_input.text().strip(),))
            existing_record = cursor.fetchone()
            
            if existing_record:
                # تحديث السجل الموجود - تحديث معلومات الاتصال فقط
                cursor.execute('''
                    UPDATE جدول_البيانات 
                    SET اسم_التلميذ = ?, النوع = ?, رقم_الهاتف_الأول = ?, 
                        رقم_الهاتف_الثاني = ?, ملاحظات = ?, تاريخ_التحديث = CURRENT_TIMESTAMP
                    WHERE رمز_التلميذ = ?
                ''', (
                    self.student_name_input.text().strip(),
                    self.gender_combo.currentText(),
                    self.phone_input.text().strip(),
                    self.phone2_input.text().strip(),
                    self.address_input.toPlainText().strip(),
                    self.student_code_input.text().strip()
                ))
                message = "تم تحديث معلومات الاتصال بنجاح."
            else:
                # إدراج سجل جديد
                cursor.execute('''
                    INSERT INTO جدول_البيانات 
                    (اسم_التلميذ, رمز_التلميذ, النوع, رقم_الهاتف_الأول, رقم_الهاتف_الثاني, ملاحظات)
                    VALUES (?, ?, ?, ?, ?, ?)
                ''', (
                    self.student_name_input.text().strip(),
                    self.student_code_input.text().strip(),
                    self.gender_combo.currentText(),
                    self.phone_input.text().strip(),
                    self.phone2_input.text().strip(),
                    self.address_input.toPlainText().strip()
                ))
                message = "تم حفظ معلومات الاتصال بنجاح."
            
            conn.commit()
            conn.close()
            
            QMessageBox.information(self, "نجح", message)
            
        except sqlite3.IntegrityError as e:
            QMessageBox.critical(self, "خطأ", "رمز التلميذ موجود بالفعل. يرجى استخدام رمز آخر.")
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في حفظ معلومات الاتصال: {str(e)}")
            
    def clear_contact_info(self):
        """مسح معلومات الاتصال"""
        reply = QMessageBox.question(
            self, "تأكيد", "هل أنت متأكد من مسح جميع معلومات الاتصال؟",
            QMessageBox.Yes | QMessageBox.No, QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            self.student_name_input.clear()
            self.student_code_input.clear()
            self.gender_combo.setCurrentIndex(0)
            self.phone_input.clear()
            self.phone2_input.clear()
            self.address_input.clear()

    def add_new_group(self):
        """إضافة مجموعة جديدة"""
        from PyQt5.QtWidgets import QInputDialog
        
        text, ok = QInputDialog.getText(
            self, 
            'إضافة مجموعة جديدة', 
            'اسم المجموعة الجديدة:',
            QLineEdit.Normal,
            ''
        )
        
        if ok and text.strip():
            # التحقق من عدم وجود المجموعة مسبقاً
            existing_items = [self.group_combo.itemText(i) for i in range(self.group_combo.count())]
            if text.strip() not in existing_items:
                self.group_combo.addItem(text.strip())
                self.group_combo.setCurrentText(text.strip())
                QMessageBox.information(self, "نجح", f"تم إضافة المجموعة '{text.strip()}' بنجاح.")
            else:
                QMessageBox.warning(self, "تحذير", "هذه المجموعة موجودة بالفعل.")
    
    def add_new_section(self):
        """إضافة قسم جديد"""
        from PyQt5.QtWidgets import QInputDialog
        
        text, ok = QInputDialog.getText(
            self, 
            'إضافة قسم جديد', 
            'اسم القسم الجديد:',
            QLineEdit.Normal,
            ''
        )
        
        if ok and text.strip():
            # التحقق من عدم وجود القسم مسبقاً
            existing_items = [self.section_combo.itemText(i) for i in range(self.section_combo.count())]
            if text.strip() not in existing_items:
                self.section_combo.addItem(text.strip())
                self.section_combo.setCurrentText(text.strip())
                QMessageBox.information(self, "نجح", f"تم إضافة القسم '{text.strip()}' بنجاح.")
            else:
                QMessageBox.warning(self, "تحذير", "هذا القسم موجود بالفعل.")
    
    def add_new_institution(self):
        """إضافة مؤسسة جديدة"""
        from PyQt5.QtWidgets import QInputDialog
        
        text, ok = QInputDialog.getText(
            self, 
            'إضافة مؤسسة جديدة', 
            'اسم المؤسسة الجديدة:',
            QLineEdit.Normal,
            ''
        )
        
        if ok and text.strip():
            # التحقق من عدم وجود المؤسسة مسبقاً
            existing_items = [self.institution_combo.itemText(i) for i in range(self.institution_combo.count())]
            if text.strip() not in existing_items:
                self.institution_combo.addItem(text.strip())
                self.institution_combo.setCurrentText(text.strip())
                QMessageBox.information(self, "نجح", f"تم إضافة المؤسسة '{text.strip()}' بنجاح.")
            else:
                QMessageBox.warning(self, "تحذير", "هذه المؤسسة موجودة بالفعل.")
    
    def save_schooling_info(self):
        """حفظ معلومات التمدرس في الجدول الموحد"""
        try:
            # التحقق من وجود رمز التلميذ
            if not self.student_code_input.text().strip():
                QMessageBox.warning(self, "تحذير", "يرجى إدخال رمز التلميذ أولاً في تبويب معلومات الاتصال.")
                return
            
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # التحقق من وجود الطالب في الجدول
            cursor.execute("SELECT id FROM جدول_البيانات WHERE رمز_التلميذ = ?", 
                          (self.student_code_input.text().strip(),))
            existing_record = cursor.fetchone()
            
            if existing_record:
                # تحديث السجل الموجود - تحديث معلومات التمدرس فقط
                cursor.execute('''
                    UPDATE جدول_البيانات 
                    SET اسم_المجموعة = ?, القسم = ?, المؤسسة_الاصلية = ?, 
                        تاريخ_التحديث = CURRENT_TIMESTAMP
                    WHERE رمز_التلميذ = ?
                ''', (
                    self.group_combo.currentText(),
                    self.section_combo.currentText(),
                    self.institution_combo.currentText(),
                    self.student_code_input.text().strip()
                ))
                message = "تم تحديث معلومات التمدرس بنجاح."
            else:
                QMessageBox.warning(self, "تحذير", "لا يوجد طالب بهذا الرمز. يرجى حفظ معلومات الاتصال أولاً.")
                conn.close()
                return
            
            conn.commit()
            conn.close()
            
            QMessageBox.information(self, "نجح", message)
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في حفظ معلومات التمدرس: {str(e)}")

    def save_registration_duties(self):
        """حفظ واجبات التسجيل في الجدول الموحد"""
        try:
            # التحقق من وجود رمز التلميذ
            if not self.student_code_input.text().strip():
                QMessageBox.warning(self, "تحذير", "يرجى إدخال رمز التلميذ أولاً في تبويب معلومات الاتصال.")
                return
            
            # التحقق من وجود مبلغ صحيح
            total_amount_text = self.total_amount_input.text().strip()
            if not total_amount_text:
                QMessageBox.warning(self, "تحذير", "يرجى إدخال إجمالي المبلغ.")
                return
            
            try:
                total_amount = float(total_amount_text)
                if total_amount <= 0:
                    QMessageBox.warning(self, "تحذير", "يرجى إدخال مبلغ صحيح أكبر من صفر.")
                    return
            except ValueError:
                QMessageBox.warning(self, "تحذير", "يرجى إدخال مبلغ صحيح.")
                return
            
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # التحقق من وجود الطالب في الجدول
            cursor.execute("SELECT id FROM جدول_البيانات WHERE رمز_التلميذ = ?", 
                          (self.student_code_input.text().strip(),))
            existing_record = cursor.fetchone()
            
            if existing_record:
                # حساب مبلغ القسط
                installments = self.installments_input.value()
                installment_amount = total_amount / installments if installments > 0 else 0
                
                # تحديث السجل الموجود - تحديث واجبات التسجيل فقط
                cursor.execute('''
                    UPDATE جدول_البيانات 
                    SET اجمالي_مبلغ_التسجيل = ?, عدد_الاقساط = ?, مبلغ_القسط = ?, 
                        تاريخ_التحديث = CURRENT_TIMESTAMP
                    WHERE رمز_التلميذ = ?
                ''', (
                    total_amount,
                    installments,
                    installment_amount,
                    self.student_code_input.text().strip()
                ))
                message = "تم حفظ واجبات التسجيل بنجاح."
            else:
                QMessageBox.warning(self, "تحذير", "لا يوجد طالب بهذا الرمز. يرجى حفظ معلومات الاتصال أولاً.")
                conn.close()
                return
            
            conn.commit()
            conn.close()
            
            QMessageBox.information(self, "نجح", message)
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في حفظ واجبات التسجيل: {str(e)}")

    def save_monthly_duties(self):
        """حفظ الواجبات الشهرية في الجدول الموحد"""
        try:
            # التحقق من وجود رمز التلميذ
            if not self.student_code_input.text().strip():
                QMessageBox.warning(self, "تحذير", "يرجى إدخال رمز التلميذ أولاً في تبويب معلومات الاتصال.")
                return
            
            # التحقق من وجود مبلغ صحيح
            monthly_duty_text = self.monthly_duty_input.text().strip()
            if not monthly_duty_text:
                QMessageBox.warning(self, "تحذير", "يرجى إدخال مبلغ الواجب الشهري.")
                return
            
            try:
                monthly_duty = float(monthly_duty_text)
                if monthly_duty <= 0:
                    QMessageBox.warning(self, "تحذير", "يرجى إدخال مبلغ صحيح أكبر من صفر.")
                    return
            except ValueError:
                QMessageBox.warning(self, "تحذير", "يرجى إدخال مبلغ صحيح.")
                return
            
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # التحقق من وجود الطالب في الجدول
            cursor.execute("SELECT id FROM جدول_البيانات WHERE رمز_التلميذ = ?", 
                          (self.student_code_input.text().strip(),))
            existing_record = cursor.fetchone()
            
            if existing_record:
                # حساب المبلغ النهائي
                selected_months = self.get_selected_months()
                total_monthly = monthly_duty * len(selected_months)
                
                # تحديث السجل الموجود - تحديث الواجبات الشهرية فقط
                cursor.execute('''
                    UPDATE جدول_البيانات 
                    SET الواجب_الشهري = ?, الاشهر_المحددة = ?, المبلغ_النهائي_الشهري = ?, 
                        تاريخ_التحديث = CURRENT_TIMESTAMP
                    WHERE رمز_التلميذ = ?
                ''', (
                    monthly_duty,
                    json.dumps(selected_months),
                    total_monthly,
                    self.student_code_input.text().strip()
                ))
                message = "تم حفظ الواجبات الشهرية بنجاح."
            else:
                QMessageBox.warning(self, "تحذير", "لا يوجد طالب بهذا الرمز. يرجى حفظ معلومات الاتصال أولاً.")
                conn.close()
                return
            
            conn.commit()
            conn.close()
            
            QMessageBox.information(self, "نجح", message)
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في حفظ الواجبات الشهرية: {str(e)}")

    def clear_schooling_info(self):
        """مسح معلومات التمدرس"""
        reply = QMessageBox.question(
            self, "تأكيد", "هل أنت متأكد من مسح جميع معلومات التمدرس؟",
            QMessageBox.Yes | QMessageBox.No, QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            self.group_combo.setCurrentIndex(0)
            self.section_combo.setCurrentIndex(0)
            self.institution_combo.setCurrentIndex(0)

    def print_registration_receipt(self):
        """طباعة توصيل واجبات التسجيل"""
        QMessageBox.information(self, "طباعة", "سيتم تطوير وظيفة طباعة توصيل واجبات التسجيل قريباً.")
        
    def print_monthly_receipt(self):
        """طباعة توصيل الواجبات الشهرية"""
        QMessageBox.information(self, "طباعة", "سيتم تطوير وظيفة طباعة توصيل الواجبات الشهرية قريباً.")

    def save_all_data(self):
        """حفظ جميع البيانات مع التحقق من الشروط"""
        try:
            # التحقق من البيانات الأساسية المطلوبة
            if not self.student_name_input.text().strip():
                QMessageBox.warning(self, "تحذير", "يرجى إدخال اسم التلميذ.")
                return
                
            if not self.student_code_input.text().strip():
                QMessageBox.warning(self, "تحذير", "يرجى إدخال رمز التلميذ.")
                return
            
            # التحقق من وجود رقم هاتف واحد على الأقل
            if not self.phone_input.text().strip() and not self.phone2_input.text().strip():
                QMessageBox.warning(self, "تحذير", "يرجى إدخال رقم هاتف واحد على الأقل.")
                return
            
            # التحقق من اختيار مجموعة التمدرس
            if not self.group_combo.currentText().strip():
                QMessageBox.warning(self, "تحذير", "يرجى اختيار مجموعة التمدرس.")
                return
            
            # التحقق من إدخال واجبات التسجيل
            total_amount_text = self.total_amount_input.text().strip()
            if not total_amount_text:
                QMessageBox.warning(self, "تحذير", "يرجى إدخال إجمالي مبلغ التسجيل.")
                return
            
            try:
                total_amount = float(total_amount_text)
                if total_amount <= 0:
                    QMessageBox.warning(self, "تحذير", "يرجى إدخال مبلغ صحيح أكبر من صفر لواجبات التسجيل.")
                    return
            except ValueError:
                QMessageBox.warning(self, "تحذير", "يرجى إدخال مبلغ صحيح لواجبات التسجيل.")
                return
            
            # التحقق من إدخال الواجب الشهري
            monthly_duty_text = self.monthly_duty_input.text().strip()
            if not monthly_duty_text:
                QMessageBox.warning(self, "تحذير", "يرجى إدخال مبلغ الواجب الشهري.")
                return
            
            try:
                monthly_duty = float(monthly_duty_text)
                if monthly_duty <= 0:
                    QMessageBox.warning(self, "تحذير", "يرجى إدخال مبلغ صحيح أكبر من صفر للواجب الشهري.")
                    return
            except ValueError:
                QMessageBox.warning(self, "تحذير", "يرجى إدخال مبلغ صحيح للواجب الشهري.")
                return
            
            # الاتصال بقاعدة البيانات
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # تحضير البيانات
            student_code = self.student_code_input.text().strip()
            group_name = self.group_combo.currentText()
            section_name = self.section_combo.currentText()
            
            # التحقق من وجود سجل بنفس الرمز والمجموعة والقسم
            cursor.execute("""
                SELECT id FROM جدول_البيانات 
                WHERE رمز_التلميذ = ? AND اسم_المجموعة = ? AND القسم = ?
            """, (student_code, group_name, section_name))
            existing_record = cursor.fetchone()
            
            # حساب القيم المحسوبة
            installments = self.installments_input.value()
            installment_amount = total_amount / installments if installments > 0 else 0
            
            selected_months = self.get_selected_months()
            total_monthly = monthly_duty * len(selected_months)
            
            if existing_record:
                # تحديث السجل الموجود
                cursor.execute('''
                    UPDATE جدول_البيانات 
                    SET اسم_التلميذ = ?, النوع = ?, رقم_الهاتف_الأول = ?, 
                        رقم_الهاتف_الثاني = ?, ملاحظات = ?,
                        المؤسسة_الاصلية = ?,
                        اجمالي_مبلغ_التسجيل = ?, عدد_الاقساط = ?, مبلغ_القسط = ?,
                        الواجب_الشهري = ?, الاشهر_المحددة = ?, المبلغ_النهائي_الشهري = ?,
                        تاريخ_التحديث = CURRENT_TIMESTAMP
                    WHERE رمز_التلميذ = ? AND اسم_المجموعة = ? AND القسم = ?
                ''', (
                    self.student_name_input.text().strip(),
                    self.gender_combo.currentText(),
                    self.phone_input.text().strip(),
                    self.phone2_input.text().strip(),
                    self.address_input.toPlainText().strip(),
                    self.institution_combo.currentText(),
                    total_amount,
                    installments,
                    installment_amount,
                    monthly_duty,
                    json.dumps(selected_months),
                    total_monthly,
                    student_code,
                    group_name,
                    section_name
                ))
                message = "تم تحديث جميع البيانات بنجاح."
            else:
                # إدراج سجل جديد
                cursor.execute('''
                    INSERT INTO جدول_البيانات 
                    (اسم_التلميذ, رمز_التلميذ, النوع, رقم_الهاتف_الأول, رقم_الهاتف_الثاني, ملاحظات,
                     اسم_المجموعة, القسم, المؤسسة_الاصلية,
                     اجمالي_مبلغ_التسجيل, عدد_الاقساط, مبلغ_القسط,
                     الواجب_الشهري, الاشهر_المحددة, المبلغ_النهائي_الشهري)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    self.student_name_input.text().strip(),
                    student_code,
                    self.gender_combo.currentText(),
                    self.phone_input.text().strip(),
                    self.phone2_input.text().strip(),
                    self.address_input.toPlainText().strip(),
                    group_name,
                    section_name,
                    self.institution_combo.currentText(),
                    total_amount,
                    installments,
                    installment_amount,
                    monthly_duty,
                    json.dumps(selected_months),
                    total_monthly
                ))
                message = "تم حفظ جميع البيانات بنجاح."
            
            conn.commit()
            conn.close()
            
            QMessageBox.information(self, "نجح", message)
            
        except sqlite3.IntegrityError as e:
            if "UNIQUE constraint failed" in str(e):
                QMessageBox.critical(self, "خطأ", "رمز التلميذ موجود بالفعل في مجموعة أخرى. يرجى التحقق من البيانات.")
            else:
                QMessageBox.critical(self, "خطأ", f"خطأ في قاعدة البيانات: {str(e)}")
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في حفظ البيانات: {str(e)}")

    def setup_add_mode(self):
        """إعداد النافذة لوضع الإضافة"""
        # مسح جميع الحقول
        self.student_name_input.clear()
        self.student_code_input.clear()
        self.gender_combo.setCurrentIndex(0)
        self.phone_input.clear()
        self.phone2_input.clear()
        self.address_input.clear()
        
        # إعداد معلومات التمدرس بالقيم الافتراضية
        self.group_combo.setCurrentIndex(0)
        self.section_combo.setCurrentIndex(0)
        self.institution_combo.setCurrentIndex(0)
        
        # مسح واجبات التسجيل
        self.total_amount_input.clear()
        self.installments_input.setValue(1)
        self.installment_amount_display.setText("0.00 درهم")
        
        # مسح الواجبات الشهرية
        self.monthly_duty_input.clear()
        self.total_monthly_display.setText("0.00 درهم")
        
        # إلغاء تحديد جميع الأشهر
        for checkbox in self.month_checkboxes.values():
            checkbox.setChecked(False)
        
        # وضع التركيز على حقل اسم التلميذ
        self.student_name_input.setFocus()

# تشغيل التطبيق للاختبار
if __name__ == "__main__":
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)
    
    window = MonthlyDutiesWindow()
    window.show()
    
    sys.exit(app.exec_())