# 📋 توثيق زر التعديل الجماعي

## 🎯 الهدف
زر التعديل الجماعي مصمم لتعديل بيانات عدة تلاميذ دفعة واحدة، مما يوفر الوقت والجهد في العمليات الإدارية.

## 🔧 الوظائف المتاحة

### 1️⃣ تغيير القسم
- تعديل قسم عدة تلاميذ إلى قسم جديد
- اختيار من قائمة الأقسام المتاحة في النظام
- تطبيق اختياري (يمكن تركه بدون تغيير)

### 2️⃣ تعديل واجبات التسجيل
- تحديث مبلغ واجبات التسجيل لعدة تلاميذ
- يتم تحديث مبلغ القسط تلقائياً ليساوي إجمالي المبلغ
- إدخال المبلغ بالدرهم مع دعم الأرقام العشرية

### 3️⃣ تعديل الواجب الشهري
- تحديث مبلغ الواجب الشهري لعدة تلاميذ
- إعادة حساب المبلغ النهائي الشهري تلقائياً
- يعتمد على الأشهر المحددة مسبقاً لكل تلميذ

## 📱 واجهة المستخدم

### تصميم النافذة
- نافذة حوارية منظمة ومقسمة إلى مجموعات
- دعم اللغة العربية والكتابة من اليمين لليسار
- ألوان احترافية ومريحة للعين

### المجموعات
1. **📚 معلومات التمدرس**: تغيير القسم
2. **💰 الواجبات المالية**: تعديل المبالغ
3. **👥 التلاميذ المحددين**: عرض قائمة التلاميذ

## 🔄 خطوات الاستخدام

### الخطوة 1: تحديد التلاميذ
```
1. اختر مجموعة من قائمة التصفية
2. حدد عدة تلاميذ من الجدول (أكثر من واحد)
3. اضغط على زر "🔄 التعديل الجماعي"
```

### الخطوة 2: اختيار التغييرات
```
1. ✅ تفعيل "تغيير القسم" واختيار القسم الجديد
2. ✅ تفعيل "تغيير واجبات التسجيل" وإدخال المبلغ
3. ✅ تفعيل "تغيير الواجب الشهري" وإدخال المبلغ
```

### الخطوة 3: التطبيق
```
1. اضغط على "✅ تطبيق التغييرات"
2. راجع ملخص التغييرات
3. أكد التطبيق
4. انتظر نتائج العملية
```

## ⚠️ شروط الاستخدام

### شروط إجبارية
- يجب تحديد **أكثر من تلميذ واحد**
- يجب تفعيل **تغيير واحد على الأقل**
- يجب تأكيد العملية قبل التطبيق

### شروط اختيارية
- يمكن تطبيق تغيير واحد أو أكثر في نفس الوقت
- يمكن ترك بعض التغييرات غير مفعلة

## 🛡️ الأمان والحماية

### التحقق من البيانات
- فحص صحة المدخلات قبل التطبيق
- التأكد من وجود التلاميذ في قاعدة البيانات
- منع التطبيق بدون تغييرات

### التأكيد
- عرض ملخص التغييرات قبل التطبيق
- إمكانية الإلغاء في أي وقت
- رسائل تأكيد واضحة

### معالجة الأخطاء
- تسجيل الأخطاء وعرضها للمستخدم
- عداد نجاح وفشل العمليات
- استمرار العملية حتى لو فشل تحديث بعض التلاميذ

## 📊 النتائج والتقارير

### رسائل النجاح
```
"تم تطبيق التغييرات بنجاح على X تلميذ."
```

### رسائل الخطأ
```
"فشل في تحديث Y تلميذ."
"تم تطبيق التغييرات بنجاح على X تلميذ. فشل في تحديث Y تلميذ."
```

### التحديث التلقائي
- تحديث الجدول الرئيسي بعد التطبيق
- عرض البيانات المحدثة فوراً
- الحفاظ على التصفية الحالية

## 🔧 التفاصيل التقنية

### قاعدة البيانات
```sql
UPDATE جدول_البيانات 
SET القسم = ?, 
    اجمالي_مبلغ_التسجيل = ?, 
    مبلغ_القسط = ?,
    الواجب_الشهري = ?,
    المبلغ_النهائي_الشهري = ?,
    تاريخ_التحديث = CURRENT_TIMESTAMP
WHERE id = ?
```

### الحسابات التلقائية
- **مبلغ القسط** = إجمالي مبلغ التسجيل (قسط واحد)
- **المبلغ النهائي الشهري** = الواجب الشهري × عدد الأشهر المحددة

### الملفات المتأثرة
- `sub252_window.py`: الملف الرئيسي
- `data.db`: قاعدة البيانات

## 🎨 التخصيص

### الألوان
- **الزر الرئيسي**: `#8e44ad` (بنفسجي)
- **زر التطبيق**: `#28a745` (أخضر)
- **زر الإلغاء**: `#dc3545` (أحمر)

### الخطوط
- **العناوين**: Calibri Bold 16px
- **النصوص**: Calibri Bold 12px
- **المعلومات**: Calibri Regular 11px

## 🚀 المميزات المستقبلية

### تحسينات مقترحة
- إضافة تعديل المجموعة
- إضافة تعديل المؤسسة الأصلية
- إضافة تعديل الأشهر المحددة
- إضافة معاينة قبل التطبيق
- إضافة تصدير تقرير التغييرات

### إمكانيات إضافية
- حفظ قوالب التعديل الجماعي
- تطبيق تغييرات مجدولة
- تتبع تاريخ التغييرات
- إمكانية التراجع عن التغييرات

---

**تم إنشاء هذا التوثيق في:** `2024-06-05`  
**الإصدار:** `1.0`  
**المطور:** Augment Agent
