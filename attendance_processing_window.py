#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import sqlite3
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout,
                             QHBoxLayout, QTableWidget, QTableWidgetItem,
                             QPushButton, QLabel, QLineEdit, QComboBox,
                             QDateEdit, QMessageBox, QAbstractItemView,
                             QHeaderView, QGroupBox, QListWidget, QDialog,
                             QDialogButtonBox, QCheckBox, QTextEdit)
from PyQt5.QtCore import Qt, QDate
from PyQt5.QtGui import QFont, QIcon

class AbsenceSessionDialog(QDialog):
    """نافذة تحديد الحصص المتغيب عنها"""
    def __init__(self, student_name, parent=None):
        super().__init__(parent)
        self.student_name = student_name
        self.selected_sessions = []
        self.init_ui()

    def init_ui(self):
        self.setWindowTitle(f"تحديد الحصص المتغيب عنها - {self.student_name}")
        self.setGeometry(300, 300, 400, 350)

        layout = QVBoxLayout(self)

        # العنوان
        title_label = QLabel(f"حدد الحصص التي غاب عنها:\n{self.student_name}")
        title_label.setFont(QFont("Calibri", 14, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                background-color: #3498db;
                color: white;
                padding: 10px;
                border-radius: 5px;
                margin-bottom: 10px;
            }
        """)
        layout.addWidget(title_label)

        # مربعات اختيار الحصص
        self.session_checkboxes = []
        sessions = ["الحصة الأولى", "الحصة الثانية", "الحصة الثالثة"]

        for session in sessions:
            checkbox = QCheckBox(session)
            checkbox.setFont(QFont("Calibri", 14, QFont.Bold))
            checkbox.setStyleSheet("""
                QCheckBox {
                    padding: 8px;
                    spacing: 10px;
                }
                QCheckBox::indicator {
                    width: 20px;
                    height: 20px;
                }
            """)
            self.session_checkboxes.append(checkbox)
            layout.addWidget(checkbox)

        # مربع الملاحظات
        notes_label = QLabel("ملاحظات إضافية:")
        notes_label.setFont(QFont("Calibri", 14, QFont.Bold))
        layout.addWidget(notes_label)

        self.notes_text = QTextEdit()
        self.notes_text.setFont(QFont("Calibri", 13, QFont.Bold))
        self.notes_text.setMaximumHeight(80)
        self.notes_text.setPlaceholderText("أدخل أي ملاحظات إضافية...")
        layout.addWidget(self.notes_text)

        # أزرار التحكم
        buttons_layout = QHBoxLayout()

        ok_button = QPushButton("✅ تأكيد")
        ok_button.setFont(QFont("Calibri", 14, QFont.Bold))
        ok_button.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border-radius: 5px;
                padding: 8px 16px;
            }
            QPushButton:hover {
                background-color: #219a52;
            }
        """)
        ok_button.clicked.connect(self.accept_selection)

        cancel_button = QPushButton("❌ إلغاء")
        cancel_button.setFont(QFont("Calibri", 14, QFont.Bold))
        cancel_button.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border-radius: 5px;
                padding: 8px 16px;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        cancel_button.clicked.connect(self.reject)

        buttons_layout.addWidget(ok_button)
        buttons_layout.addWidget(cancel_button)
        layout.addLayout(buttons_layout)

    def accept_selection(self):
        """تأكيد الاختيار"""
        self.selected_sessions = []
        for checkbox in self.session_checkboxes:
            if checkbox.isChecked():
                self.selected_sessions.append(checkbox.text())

        if not self.selected_sessions:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار حصة واحدة على الأقل")
            return

        self.accept()

    def get_selected_sessions(self):
        """الحصول على الحصص المحددة"""
        return self.selected_sessions

    def get_notes(self):
        """الحصول على الملاحظات"""
        return self.notes_text.toPlainText().strip()

class AttendanceProcessingWindow(QMainWindow):
    def __init__(self, db_path="data.db", parent=None):
        super().__init__(parent)
        self.db_path = db_path
        self.init_database()
        self.init_ui()
        self.load_sections()
        self.load_students_data()

    def init_database(self):
        """إنشاء جداول قاعدة البيانات المطلوبة"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # إنشاء جدول الغياب
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS absence_records (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    student_id INTEGER,
                    student_name TEXT,
                    student_code TEXT,
                    absence_date TEXT,
                    section TEXT,
                    sessions TEXT,
                    notes TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (student_id) REFERENCES جدول_البيانات(id)
                )
            """)
            
            # إنشاء فهارس للأداء
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_absence_date ON absence_records(absence_date)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_student_id ON absence_records(student_id)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_section ON absence_records(section)")
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            print(f"خطأ في إنشاء قاعدة البيانات: {str(e)}")

    def init_ui(self):
        """إنشاء واجهة المستخدم"""
        self.setWindowTitle("معالجة الغياب والتقارير")
        self.setGeometry(100, 100, 1200, 800)
        
        # الويدجيت الرئيسي
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        main_layout = QVBoxLayout(central_widget)
        
        # العنوان الرئيسي
        title_label = QLabel("🎯 معالجة الغياب والتقارير")
        title_label.setFont(QFont("Calibri", 15, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                background-color: #2c3e50;
                color: white;
                padding: 15px;
                border-radius: 8px;
                margin-bottom: 10px;
            }
        """)
        main_layout.addWidget(title_label)
        
        # مجموعة البحث والتصفية
        self.create_search_group(main_layout)
        
        # مجموعة الأزرار
        self.create_buttons_group(main_layout)
        
        # جدول الطلاب
        self.create_students_table(main_layout)
        
        # شريط الحالة
        self.statusBar().setFont(QFont("Calibri", 13, QFont.Bold))
        self.statusBar().showMessage("جاهز")

    def create_search_group(self, main_layout):
        """إنشاء مجموعة البحث والتصفية"""
        search_group = QGroupBox("🔍 البحث والتصفية")
        search_group.setFont(QFont("Calibri", 14, QFont.Bold))
        search_layout = QHBoxLayout(search_group)
        
        # البحث بالاسم
        name_label = QLabel("اسم التلميذ:")
        name_label.setFont(QFont("Calibri", 14, QFont.Bold))
        search_layout.addWidget(name_label)
        
        self.student_search = QLineEdit()
        self.student_search.setPlaceholderText("ابحث عن تلميذ...")
        self.student_search.setFont(QFont("Calibri", 14, QFont.Bold))
        self.student_search.textChanged.connect(self.load_students_data)
        search_layout.addWidget(self.student_search)
        
        # اختيار القسم
        section_label = QLabel("القسم:")
        section_label.setFont(QFont("Calibri", 14, QFont.Bold))
        search_layout.addWidget(section_label)
        
        self.section_combo = QComboBox()
        self.section_combo.setFont(QFont("Calibri", 14, QFont.Bold))
        self.section_combo.currentTextChanged.connect(self.load_students_data)
        search_layout.addWidget(self.section_combo)
        
        # اختيار التاريخ
        date_label = QLabel("التاريخ:")
        date_label.setFont(QFont("Calibri", 14, QFont.Bold))
        search_layout.addWidget(date_label)
        
        self.date_edit = QDateEdit()
        self.date_edit.setDate(QDate.currentDate())
        self.date_edit.setFont(QFont("Calibri", 14, QFont.Bold))
        self.date_edit.setCalendarPopup(True)
        self.date_edit.dateChanged.connect(self.load_students_data)
        search_layout.addWidget(self.date_edit)
        
        # زر تحديث
        refresh_btn = QPushButton("🔄 تحديث")
        refresh_btn.setFont(QFont("Calibri", 14, QFont.Bold))
        refresh_btn.setStyleSheet("""
            QPushButton {
                background-color: #17a2b8;
                color: white;
                border-radius: 6px;
                padding: 8px 16px;
            }
            QPushButton:hover {
                background-color: #138496;
            }
        """)
        refresh_btn.clicked.connect(self.refresh_all_data)
        search_layout.addWidget(refresh_btn)
        
        main_layout.addWidget(search_group)

    def create_buttons_group(self, main_layout):
        """إنشاء مجموعة الأزرار"""
        buttons_group = QGroupBox("⚡ العمليات")
        buttons_group.setFont(QFont("Calibri", 14, QFont.Bold))
        buttons_layout = QHBoxLayout(buttons_group)
        
        # زر تسجيل غياب فردي
        absent_btn = QPushButton("❌ تسجيل غياب")
        absent_btn.setFont(QFont("Calibri", 14, QFont.Bold))
        absent_btn.setStyleSheet("""
            QPushButton {
                background-color: #dc3545;
                color: white;
                border-radius: 6px;
                padding: 10px 20px;
            }
            QPushButton:hover {
                background-color: #c82333;
            }
        """)
        absent_btn.clicked.connect(self.mark_student_absent)
        buttons_layout.addWidget(absent_btn)
        
        # زر غياب جماعي
        bulk_absent_btn = QPushButton("📋 غياب جماعي")
        bulk_absent_btn.setFont(QFont("Calibri", 14, QFont.Bold))
        bulk_absent_btn.setStyleSheet("""
            QPushButton {
                background-color: #fd7e14;
                color: white;
                border-radius: 6px;
                padding: 10px 20px;
            }
            QPushButton:hover {
                background-color: #e8650e;
            }
        """)
        bulk_absent_btn.clicked.connect(self.bulk_mark_absent)
        buttons_layout.addWidget(bulk_absent_btn)
        
        # زر تقرير الغياب
        report_btn = QPushButton("📊 تقرير الغياب")
        report_btn.setFont(QFont("Calibri", 14, QFont.Bold))
        report_btn.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                border-radius: 6px;
                padding: 10px 20px;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        report_btn.clicked.connect(self.generate_absence_report)
        buttons_layout.addWidget(report_btn)
        
        # زر إحصائيات
        stats_btn = QPushButton("📈 الإحصائيات")
        stats_btn.setFont(QFont("Calibri", 14, QFont.Bold))
        stats_btn.setStyleSheet("""
            QPushButton {
                background-color: #6f42c1;
                color: white;
                border-radius: 6px;
                padding: 10px 20px;
            }
            QPushButton:hover {
                background-color: #5a32a3;
            }
        """)
        stats_btn.clicked.connect(self.show_statistics)
        buttons_layout.addWidget(stats_btn)

        # زر ورقة متابعة الغياب
        sheet_btn = QPushButton("📋 ورقة المتابعة")
        sheet_btn.setFont(QFont("Calibri", 14, QFont.Bold))
        sheet_btn.setStyleSheet("""
            QPushButton {
                background-color: #17a2b8;
                color: white;
                border-radius: 6px;
                padding: 10px 20px;
            }
            QPushButton:hover {
                background-color: #138496;
            }
        """)
        sheet_btn.clicked.connect(self.open_attendance_sheet)
        buttons_layout.addWidget(sheet_btn)
        
        main_layout.addWidget(buttons_group)

    def create_students_table(self, main_layout):
        """إنشاء جدول الطلاب"""
        table_group = QGroupBox("👥 قائمة التلاميذ")
        table_group.setFont(QFont("Calibri", 14, QFont.Bold))
        table_layout = QVBoxLayout(table_group)
        
        self.students_table = QTableWidget()
        self.students_table.setColumnCount(3)
        self.students_table.setHorizontalHeaderLabels([
            "عدد حصص الغياب", "رمز التلميذ", "اسم التلميذ"
        ])
        
        # تنسيق رؤوس الجدول
        header = self.students_table.horizontalHeader()
        header.setFont(QFont("Calibri", 14, QFont.Bold))
        header.setStyleSheet("""
            QHeaderView::section {
                background-color: #34495e;
                color: white;
                padding: 8px;
                border: 1px solid #2c3e50;
                font-weight: bold;
            }
        """)
        
        # تنسيق الجدول
        self.students_table.setAlternatingRowColors(True)
        self.students_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.students_table.setSelectionMode(QAbstractItemView.MultiSelection)
        self.students_table.setSortingEnabled(True)
        
        # تعيين عرض الأعمدة
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)  # عدد الغياب
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)  # رمز التلميذ
        header.setSectionResizeMode(2, QHeaderView.Stretch)  # اسم التلميذ
        
        # تنسيق الجدول
        self.students_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #bdc3c7;
                background-color: white;
                alternate-background-color: #f8f9fa;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #dee2e6;
            }
            QTableWidget::item:selected {
                background-color: #007bff;
                color: white;
            }
        """)
        
        table_layout.addWidget(self.students_table)
        main_layout.addWidget(table_group)

    def load_sections(self):
        """تحميل الأقسام"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            self.section_combo.clear()
            self.section_combo.addItem("جميع الأقسام")
            
            # جلب الأقسام من جدول البيانات
            cursor.execute("SELECT DISTINCT القسم FROM جدول_البيانات WHERE القسم IS NOT NULL AND القسم != '' ORDER BY القسم")
            sections = cursor.fetchall()
            
            for section in sections:
                if section[0]:
                    self.section_combo.addItem(section[0])
            
            conn.close()
            print(f"✅ تم تحميل {len(sections)} قسم")
            
        except Exception as e:
            self.statusBar().showMessage(f"خطأ في تحميل الأقسام: {str(e)}")
            print(f"❌ خطأ في تحميل الأقسام: {e}")

    def load_students_data(self):
        """تحميل بيانات الطلاب"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # إعداد التصفية
            search_text = self.student_search.text().strip() if hasattr(self, 'student_search') else ""
            selected_section = self.section_combo.currentText() if hasattr(self, 'section_combo') else "جميع الأقسام"

            # بناء الاستعلام
            where_conditions = ["اسم_التلميذ IS NOT NULL", "اسم_التلميذ != ''"]
            params = []

            # تصفية بالاسم
            if search_text:
                where_conditions.append("اسم_التلميذ LIKE ?")
                params.append(f"%{search_text}%")

            # تصفية بالقسم
            if selected_section != "جميع الأقسام":
                where_conditions.append("القسم = ?")
                params.append(selected_section)

            query = f"""
                SELECT
                    id,
                    اسم_التلميذ,
                    رمز_التلميذ
                FROM جدول_البيانات
                WHERE {' AND '.join(where_conditions)}
                ORDER BY اسم_التلميذ
            """

            print(f"🔍 الاستعلام: {query}")
            print(f"🔍 المعاملات: {params}")

            cursor.execute(query, params)
            students_basic = cursor.fetchall()

            print(f"📊 تم جلب {len(students_basic)} تلميذ من قاعدة البيانات")

            if len(students_basic) == 0:
                print("⚠️ لا توجد بيانات تلاميذ!")
                # اختبار استعلام بسيط
                cursor.execute("SELECT COUNT(*) FROM جدول_البيانات WHERE اسم_التلميذ IS NOT NULL")
                total_count = cursor.fetchone()[0]
                print(f"📊 إجمالي التلاميذ في قاعدة البيانات: {total_count}")

                if total_count > 0:
                    cursor.execute("SELECT اسم_التلميذ, رمز_التلميذ, القسم FROM جدول_البيانات WHERE اسم_التلميذ IS NOT NULL LIMIT 5")
                    sample = cursor.fetchall()
                    print(f"📝 عينة من البيانات: {sample}")

            # معالجة البيانات وحساب الغياب
            students = []
            for i, student_basic in enumerate(students_basic):
                if i < 5:  # طباعة أول 5 للتشخيص
                    print(f"   📋 معالجة التلميذ {i+1}: {student_basic}")

                student_id, student_name, student_code = student_basic

                # حساب عدد حصص الغياب (كل سجل = حصة واحدة)
                cursor.execute("""
                    SELECT COUNT(*)
                    FROM absence_records
                    WHERE student_id = ?
                """, (student_id,))

                absence_count = cursor.fetchone()[0]

                students.append((
                    student_name,
                    student_code or "غير محدد",
                    absence_count
                ))

            print(f"📊 تم معالجة {len(students)} تلميذ")

            # عرض البيانات في الجدول
            self.students_table.setRowCount(len(students))
            print(f"🔧 تم تعيين عدد الصفوف: {len(students)}")

            for row, student in enumerate(students):
                try:
                    if row < 3:  # طباعة أول 3 صفوف للتشخيص
                        print(f"   📋 عرض الصف {row}: {student}")

                    # عدد حصص الغياب (العمود الأول)
                    absence_item = QTableWidgetItem(str(student[2]))
                    absence_item.setFont(QFont("Calibri", 14, QFont.Bold))
                    absence_item.setTextAlignment(Qt.AlignCenter)

                    # تلوين حسب عدد الغياب
                    if student[2] > 10:
                        absence_item.setStyleSheet("background-color: #fadbd8; color: #e74c3c;")
                    elif student[2] > 5:
                        absence_item.setStyleSheet("background-color: #fff3cd; color: #856404;")
                    else:
                        absence_item.setStyleSheet("background-color: #d5f4e6; color: #27ae60;")

                    self.students_table.setItem(row, 0, absence_item)

                    # رمز التلميذ (العمود الثاني)
                    code_item = QTableWidgetItem(str(student[1]))
                    code_item.setFont(QFont("Calibri", 14, QFont.Bold))
                    code_item.setTextAlignment(Qt.AlignCenter)
                    self.students_table.setItem(row, 1, code_item)

                    # اسم التلميذ (العمود الثالث)
                    name_item = QTableWidgetItem(str(student[0]))
                    name_item.setFont(QFont("Calibri", 14, QFont.Bold))
                    self.students_table.setItem(row, 2, name_item)

                    if row < 3:
                        print(f"      ✅ تم عرض الصف {row} بنجاح")

                except Exception as e:
                    print(f"❌ خطأ في عرض الصف {row}: {e}")

            # فحص نهائي
            actual_rows = self.students_table.rowCount()
            print(f"🔍 فحص نهائي: الجدول يحتوي على {actual_rows} صف")

            conn.close()
            self.statusBar().showMessage(f"تم تحميل {len(students)} تلميذ")

        except Exception as e:
            self.statusBar().showMessage(f"خطأ في تحميل البيانات: {str(e)}")
            print(f"❌ خطأ في تحميل البيانات: {e}")
            import traceback
            traceback.print_exc()

    def mark_student_absent(self):
        """تسجيل غياب التلميذ المحدد"""
        selected_rows = self.students_table.selectionModel().selectedRows()
        if not selected_rows:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار تلميذ لتسجيل غيابه")
            return
        
        self._process_absence(selected_rows)

    def bulk_mark_absent(self):
        """تسجيل غياب جماعي"""
        selected_rows = self.students_table.selectionModel().selectedRows()
        if not selected_rows:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار التلاميذ لتسجيل غيابهم")
            return
        
        reply = QMessageBox.question(
            self, "تأكيد الغياب الجماعي",
            f"هل أنت متأكد من تسجيل غياب {len(selected_rows)} تلميذ؟",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            self._process_absence(selected_rows)

    def _process_absence(self, selected_rows):
        """معالجة تسجيل الغياب"""
        try:
            print(f"🔧 بدء معالجة تسجيل الغياب لـ {len(selected_rows)} تلميذ")

            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            current_date = self.date_edit.date().toString("yyyy-MM-dd")
            success_count = 0

            for i, index in enumerate(selected_rows):
                try:
                    row = index.row()
                    print(f"   📋 معالجة التلميذ {i+1} - الصف {row}")

                    # التحقق من وجود البيانات في الصف
                    if (not self.students_table.item(row, 2) or
                        not self.students_table.item(row, 1)):
                        print(f"   ⚠️ بيانات ناقصة في الصف {row}")
                        continue

                    # الأعمدة معكوسة: 0=غياب، 1=رمز، 2=اسم
                    student_name = self.students_table.item(row, 2).text()
                    student_code = self.students_table.item(row, 1).text()

                    print(f"      👤 التلميذ: {student_name} - الرمز: {student_code}")

                    # فتح نافذة تحديد الحصص
                    session_dialog = AbsenceSessionDialog(student_name, self)
                    if session_dialog.exec_() == QDialog.Accepted:
                        selected_sessions = session_dialog.get_selected_sessions()
                        notes = session_dialog.get_notes()

                        print(f"      📝 الحصص المحددة: {selected_sessions}")

                        if not selected_sessions:
                            print(f"      ⚠️ لم يتم اختيار حصص للتلميذ {student_name}")
                            continue

                        # البحث عن معرف التلميذ
                        cursor.execute("SELECT id, القسم FROM جدول_البيانات WHERE اسم_التلميذ = ?", (student_name,))
                        student_result = cursor.fetchone()

                        if student_result:
                            student_id, section = student_result
                            print(f"      🆔 معرف التلميذ: {student_id} - القسم: {section}")

                            # تسجيل كل حصة منفصلة
                            for session in selected_sessions:
                                try:
                                    # التحقق من عدم وجود تسجيل غياب لنفس الحصة في نفس اليوم
                                    cursor.execute("""
                                        SELECT id FROM absence_records
                                        WHERE student_id = ? AND DATE(absence_date) = ? AND sessions = ?
                                    """, (student_id, current_date, session))

                                    existing_record = cursor.fetchone()
                                    if not existing_record:
                                        cursor.execute("""
                                            INSERT INTO absence_records
                                            (student_id, student_name, student_code, absence_date, section, sessions, notes)
                                            VALUES (?, ?, ?, ?, ?, ?, ?)
                                        """, (student_id, student_name, student_code, current_date, section, session, notes))
                                        success_count += 1
                                        print(f"         ✅ تم تسجيل {session} للتلميذ {student_name}")
                                    else:
                                        print(f"         ⚠️ {session} مسجلة مسبقاً للتلميذ {student_name}")

                                except Exception as session_error:
                                    print(f"         ❌ خطأ في تسجيل {session}: {session_error}")
                        else:
                            print(f"      ❌ لم يتم العثور على التلميذ {student_name} في قاعدة البيانات")
                    else:
                        print(f"      ❌ تم إلغاء تسجيل الغياب للتلميذ {student_name}")

                except Exception as student_error:
                    print(f"   ❌ خطأ في معالجة التلميذ {i+1}: {student_error}")
                    continue

            conn.commit()
            conn.close()

            print(f"✅ انتهاء المعالجة - تم تسجيل {success_count} حصة غياب")

            if success_count > 0:
                QMessageBox.information(self, "نجح", f"تم تسجيل {success_count} حصة غياب")
                self.load_students_data()  # إعادة تحميل البيانات
            else:
                QMessageBox.warning(self, "تنبيه", "لم يتم تسجيل أي حصة غياب")

        except Exception as e:
            error_msg = f"حدث خطأ في تسجيل الغياب:\n{str(e)}"
            print(f"❌ {error_msg}")
            QMessageBox.critical(self, "خطأ", error_msg)
            import traceback
            traceback.print_exc()

    def generate_absence_report(self):
        """إنشاء تقرير الغياب"""
        QMessageBox.information(self, "تقرير الغياب", "سيتم إنشاء تقرير الغياب قريباً")

    def show_statistics(self):
        """عرض الإحصائيات"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # إحصائيات عامة
            cursor.execute("SELECT COUNT(*) FROM جدول_البيانات")
            total_students = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(DISTINCT student_id) FROM absence_records")
            students_with_absence = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(*) FROM absence_records")
            total_absences = cursor.fetchone()[0]
            
            current_date = self.date_edit.date().toString("yyyy-MM-dd")
            cursor.execute("SELECT COUNT(*) FROM absence_records WHERE DATE(absence_date) = ?", (current_date,))
            today_absences = cursor.fetchone()[0]
            
            conn.close()
            
            stats_text = f"""
📊 إحصائيات الغياب:

👥 إجمالي التلاميذ: {total_students}
❌ تلاميذ لديهم غياب: {students_with_absence}
📈 إجمالي حصص الغياب: {total_absences}
📅 غياب اليوم: {today_absences}
            """
            
            QMessageBox.information(self, "إحصائيات الغياب", stats_text)
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في عرض الإحصائيات:\n{str(e)}")

    def refresh_all_data(self):
        """تحديث جميع البيانات"""
        self.load_sections()
        self.load_students_data()
        self.statusBar().showMessage("تم تحديث جميع البيانات")

    def open_attendance_sheet(self):
        """فتح نافذة ورقة متابعة الغياب"""
        try:
            from monthly_attendance_sheet import MonthlyAttendanceSheet
            self.attendance_sheet_window = MonthlyAttendanceSheet(self.db_path, self)
            self.attendance_sheet_window.show()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في فتح ورقة المتابعة: {str(e)}")

def main():
    app = QApplication(sys.argv)
    app.setStyle('Fusion')
    
    window = AttendanceProcessingWindow()
    window.show()
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
